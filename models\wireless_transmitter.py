from datetime import datetime
from models.database import db

class WirelessTransmitter(db.Model):
    """无线充电发射端模型"""
    __tablename__ = 'wireless_transmitter'

    # 主键
    id = db.Column(db.Integer, primary_key=True)
    
    # 发射端ID - uint64整数
    transmitter_id = db.Column(db.BigInteger, unique=True, nullable=False, index=True)
    
    # 发射端备注
    transmitter_remark = db.Column(db.String(200), default="")
    
    # 发射端硬件版本：1=电动自行车，2=电动三轮车
    hardware_version = db.Column(db.Integer, nullable=False, default=1)
    
    # 发射端软件版本 - 存储为整数，显示时转换为major.minor.patch格式
    software_version = db.Column(db.Integer, nullable=False, default=0)
    
    # 发射端注册日期
    register_date = db.Column(db.DateTime, default=datetime.now, nullable=False)
    
    # 发射端绑定的中控ID - uint64整数，独立存储，不与现有中控ID关联
    controller_id = db.Column(db.BigInteger, nullable=True, default=None)
    
    # 发射端当前所属主体
    owning_entity = db.Column(db.String(100), default="")
    
    # 创建和更新时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    @staticmethod
    def get_hardware_version_name(hardware_version_value):
        """将硬件版本数值转换为对应的名称"""
        hardware_version_map = {
            1: "电动自行车",
            2: "电动三轮车"
        }
        return hardware_version_map.get(hardware_version_value, f"未知类型 ({hardware_version_value})") if hardware_version_value else "未设置"

    @property
    def hardware_version_name(self):
        """获取硬件版本名称"""
        return self.get_hardware_version_name(self.hardware_version)

    @property
    def software_version_string(self):
        """将软件版本整数转换为major.minor.patch格式"""
        if self.software_version is None or self.software_version == 0:
            return "0.0.0"
        
        major = (self.software_version >> 16) & 0xFF
        minor = (self.software_version >> 8) & 0xFF
        patch = self.software_version & 0xFF
        return f"{major}.{minor}.{patch}"

    @staticmethod
    def version_string_to_int(version_string):
        """将major.minor.patch格式的版本字符串转换为整数"""
        try:
            parts = version_string.split('.')
            if len(parts) != 3:
                return 0
            
            major = int(parts[0]) & 0xFF
            minor = int(parts[1]) & 0xFF
            patch = int(parts[2]) & 0xFF
            
            return (major << 16) | (minor << 8) | patch
        except (ValueError, IndexError):
            return 0

    def to_dict(self):
        """转换为字典格式，用于API返回"""
        return {
            'id': self.id,
            'transmitter_id': self.transmitter_id,
            'transmitter_remark': self.transmitter_remark,
            'hardware_version': self.hardware_version,
            'hardware_version_name': self.hardware_version_name,
            'software_version': self.software_version,
            'software_version_string': self.software_version_string,
            'register_date': self.register_date.isoformat() if self.register_date else None,
            'controller_id': self.controller_id,
            'owning_entity': self.owning_entity,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<WirelessTransmitter {self.transmitter_id}>'
