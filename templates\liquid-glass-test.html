{% extends "base.html" %}

{% block title %}液态玻璃主题测试页面{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">液态玻璃主题测试页面</h1>
            <p class="text-center text-muted">此页面用于测试液态玻璃主题的各种组件效果</p>
        </div>
    </div>

    <!-- 主题切换测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">主题切换测试</h5>
                </div>
                <div class="card-body">
                    <p>请使用页面右下角的主题切换器切换到"液态玻璃"主题，然后观察页面样式是否正确显示。</p>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="testThemeSwitch('liquidGlass')">切换到液态玻璃</button>
                        <button class="btn btn-secondary" onclick="testThemeSwitch('default')">切换到默认主题</button>
                        <button class="btn btn-info" onclick="checkCurrentTheme()">检查当前主题</button>
                    </div>
                    <div id="themeStatus" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组件测试 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">按钮测试</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-success">成功按钮</button>
                        <button class="btn btn-warning">警告按钮</button>
                        <button class="btn btn-danger">危险按钮</button>
                        <button class="btn btn-info">信息按钮</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">表单测试</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label for="testInput" class="form-label">测试输入框</label>
                            <input type="text" class="form-control" id="testInput" placeholder="请输入内容">
                        </div>
                        <div class="mb-3">
                            <label for="testSelect" class="form-label">测试选择框</label>
                            <select class="form-select" id="testSelect">
                                <option selected>选择选项</option>
                                <option value="1">选项 1</option>
                                <option value="2">选项 2</option>
                                <option value="3">选项 3</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 警告框测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-primary" role="alert">
                <i class="fas fa-info-circle me-2"></i>这是一个主要警告框，用于测试液态玻璃效果。
            </div>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>这是一个成功警告框，用于测试液态玻璃效果。
            </div>
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>这是一个警告警告框，用于测试液态玻璃效果。
            </div>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-times-circle me-2"></i>这是一个危险警告框，用于测试液态玻璃效果。
            </div>
        </div>
    </div>

    <!-- 表格测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">表格测试</h5>
                </div>
                <div class="card-body">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>测试项目 1</td>
                                <td><span class="badge bg-success">在线</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">编辑</button>
                                    <button class="btn btn-sm btn-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>测试项目 2</td>
                                <td><span class="badge bg-warning">离线</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">编辑</button>
                                    <button class="btn btn-sm btn-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>测试项目 3</td>
                                <td><span class="badge bg-success">在线</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">编辑</button>
                                    <button class="btn btn-sm btn-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度条测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">进度条测试</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">基本进度条</label>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">25%</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">成功进度条</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">50%</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">警告进度条</label>
                        <div class="progress">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">75%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 样式检查结果 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">样式检查结果</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-info" onclick="checkStyles()">检查液态玻璃样式</button>
                    <div id="styleCheckResult" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function testThemeSwitch(theme) {
    if (window.themeSwitcher) {
        window.themeSwitcher.switchTheme(theme);
        updateThemeStatus();
    } else {
        document.getElementById('themeStatus').innerHTML = 
            '<div class="alert alert-danger">主题切换器未加载</div>';
    }
}

function checkCurrentTheme() {
    updateThemeStatus();
}

function updateThemeStatus() {
    const statusDiv = document.getElementById('themeStatus');
    if (window.themeSwitcher) {
        const currentTheme = window.themeSwitcher.getCurrentTheme();
        const themeInfo = window.themeSwitcher.getThemeInfo(currentTheme);
        const hasLiquidGlassClass = document.body.classList.contains('liquid-glass-theme');
        
        statusDiv.innerHTML = `
            <div class="alert alert-info">
                <strong>当前主题:</strong> ${themeInfo.name} (${currentTheme})<br>
                <strong>Body类名:</strong> ${hasLiquidGlassClass ? '包含 liquid-glass-theme' : '不包含 liquid-glass-theme'}<br>
                <strong>主题描述:</strong> ${themeInfo.description}
            </div>
        `;
    } else {
        statusDiv.innerHTML = '<div class="alert alert-danger">主题切换器未加载</div>';
    }
}

function checkStyles() {
    const resultDiv = document.getElementById('styleCheckResult');
    const body = document.body;
    const isLiquidGlass = body.classList.contains('liquid-glass-theme');
    
    if (!isLiquidGlass) {
        resultDiv.innerHTML = '<div class="alert alert-warning">当前不是液态玻璃主题，请先切换到液态玻璃主题</div>';
        return;
    }
    
    // 检查CSS变量
    const styles = getComputedStyle(document.documentElement);
    const glassBlur = styles.getPropertyValue('--glass-blur').trim();
    const glassBg = styles.getPropertyValue('--glass-bg').trim();
    const glassBorder = styles.getPropertyValue('--glass-border').trim();
    
    // 检查背景样式
    const bodyStyles = getComputedStyle(body);
    const background = bodyStyles.background;
    const hasGradient = background.includes('linear-gradient');
    
    // 检查卡片样式
    const card = document.querySelector('.card');
    const cardStyles = card ? getComputedStyle(card) : null;
    const cardBackdropFilter = cardStyles ? cardStyles.backdropFilter : '';
    
    let result = '<div class="alert alert-info"><h6>液态玻璃样式检查结果:</h6><ul>';
    result += `<li><strong>CSS变量 --glass-blur:</strong> ${glassBlur || '未定义'}</li>`;
    result += `<li><strong>CSS变量 --glass-bg:</strong> ${glassBg || '未定义'}</li>`;
    result += `<li><strong>CSS变量 --glass-border:</strong> ${glassBorder || '未定义'}</li>`;
    result += `<li><strong>Body背景渐变:</strong> ${hasGradient ? '✅ 已应用' : '❌ 未应用'}</li>`;
    result += `<li><strong>卡片毛玻璃效果:</strong> ${cardBackdropFilter ? '✅ 已应用' : '❌ 未应用'}</li>`;
    result += '</ul></div>';
    
    resultDiv.innerHTML = result;
}

// 页面加载时检查主题状态
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateThemeStatus, 500);
});

// 监听主题变化
document.addEventListener('themeChanged', function(e) {
    console.log('主题已切换:', e.detail);
    setTimeout(updateThemeStatus, 100);
});
</script>
{% endblock %}
