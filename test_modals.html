<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>模态框功能测试</h1>
        <div class="row">
            <div class="col-12">
                <button class="btn btn-primary me-2" onclick="queryFirmwareInfo()">
                    <i class="fas fa-microchip me-1"></i> 查询固件信息
                </button>
                <button class="btn btn-success me-2" onclick="querySimInfo()">
                    <i class="fas fa-sim-card me-1"></i> 查询SIM卡信息
                </button>
                <button class="btn btn-warning me-2" onclick="queryErrorCountsModal()">
                    <i class="fas fa-exclamation-triangle me-1"></i> 查询错误计数
                </button>
                <button class="btn btn-info me-2" onclick="queryDeviceLocationModal()">
                    <i class="fas fa-map-marker-alt me-1"></i> 查询位置
                </button>
            </div>
        </div>
    </div>

    <!-- 固件信息查询模态框 -->
    <div class="modal fade" id="firmwareInfoModal" tabindex="-1" aria-labelledby="firmwareInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="firmwareInfoModalLabel">设备固件信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="firmwareInfoLoading" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在获取设备固件信息...</p>
                    </div>
                    <div id="firmwareInfoContent" class="d-none">
                        <p>固件信息内容将在这里显示</p>
                    </div>
                    <div id="firmwareInfoError" class="alert alert-danger d-none">
                        获取设备固件信息失败，请重试。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="queryFirmwareInfo(true)">刷新</button>
                </div>
            </div>
        </div>
    </div>

    <!-- SIM卡信息查询模态框 -->
    <div class="modal fade" id="simInfoModal" tabindex="-1" aria-labelledby="simInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="simInfoModalLabel">设备SIM卡信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="simInfoLoading" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在获取设备SIM卡信息...</p>
                    </div>
                    <div id="simInfoContent" class="d-none">
                        <p>SIM卡信息内容将在这里显示</p>
                    </div>
                    <div id="simInfoError" class="alert alert-danger d-none">
                        获取设备SIM卡信息失败，请重试。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="querySimInfo(true)">刷新</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 设置全局设备ID，供模态框组件使用
        window.deviceId = 75;

        // 查询设备固件信息
        function queryFirmwareInfo(isRefresh = false) {
            // 显示模态框
            if (!isRefresh) {
                const modal = new bootstrap.Modal(document.getElementById('firmwareInfoModal'));
                modal.show();
            }

            // 显示加载中状态
            document.getElementById('firmwareInfoLoading').classList.remove('d-none');
            document.getElementById('firmwareInfoContent').classList.add('d-none');
            document.getElementById('firmwareInfoError').classList.add('d-none');

            // 模拟API调用
            setTimeout(() => {
                document.getElementById('firmwareInfoLoading').classList.add('d-none');
                document.getElementById('firmwareInfoContent').classList.remove('d-none');
                document.getElementById('firmwareInfoContent').innerHTML = '<p>固件信息查询功能正常工作！</p>';
            }, 2000);
        }

        // 查询设备SIM卡信息
        function querySimInfo(isRefresh = false) {
            // 显示模态框
            if (!isRefresh) {
                const modal = new bootstrap.Modal(document.getElementById('simInfoModal'));
                modal.show();
            }

            // 显示加载中状态
            document.getElementById('simInfoLoading').classList.remove('d-none');
            document.getElementById('simInfoContent').classList.add('d-none');
            document.getElementById('simInfoError').classList.add('d-none');

            // 模拟API调用
            setTimeout(() => {
                document.getElementById('simInfoLoading').classList.add('d-none');
                document.getElementById('simInfoContent').classList.remove('d-none');
                document.getElementById('simInfoContent').innerHTML = '<p>SIM卡信息查询功能正常工作！</p>';
            }, 2000);
        }

        // 查询错误计数（模态框版本）
        function queryErrorCountsModal() {
            alert('错误计数查询功能正常工作！');
        }

        // 查询设备位置（模态框版本）
        function queryDeviceLocationModal() {
            alert('设备位置查询功能正常工作！');
        }
    </script>
</body>
</html>
