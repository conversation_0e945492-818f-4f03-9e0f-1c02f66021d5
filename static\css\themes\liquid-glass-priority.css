/* 
 * Liquid Glass Priority Styles
 * 高优先级样式，确保液态玻璃效果不被其他样式覆盖
 */

/* ===== 核心液态玻璃样式 - 高优先级 ===== */

/* 确保body背景样式不被覆盖 */
body.liquid-glass-theme {
    background: linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 25%, 
        #f093fb 50%, 
        #f5576c 75%, 
        #4facfe 100%) !important;
    background-size: 400% 400% !important;
    animation: gradientShift 15s ease infinite !important;
    min-height: 100vh !important;
    position: relative !important;
    padding-top: 70px !important;
    font-family: 'Noto Sans SC', sans-serif !important;
}

.bg-white {
    --bs-bg-opacity: 1;
    background-color: transparent !important;
}

.device-table .action-column {
    background-color: transparent !important;
}

/* 确保导航栏样式不被覆盖 */
body.liquid-glass-theme .navbar {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border-bottom: 1px solid var(--glass-border) !important;
    box-shadow: var(--glass-shadow) !important;
}

/* 确保卡片样式不被覆盖 */
body.liquid-glass-theme .card {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    box-shadow: var(--glass-shadow) !important;
    border-radius: 20px !important;
    transition: var(--transition-smooth) !important;
}

/* 确保卡片悬停效果不被覆盖 */
body.liquid-glass-theme .card:hover {
    background: var(--glass-bg-light) !important;
    box-shadow: var(--glass-shadow-hover) !important;
    transform: var(--hover-scale) !important;
}

/* 确保按钮样式不被覆盖 */
body.liquid-glass-theme .btn {
    background: var(--primary-glass) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 25px !important;
    color: white !important;
    font-weight: 500 !important;
    transition: var(--transition-smooth) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 确保按钮悬停效果不被覆盖 */
body.liquid-glass-theme .btn:hover {
    background: var(--primary-glass-hover) !important;
    transform: var(--hover-scale) !important;
    box-shadow: var(--glass-shadow-hover) !important;
}

/* 确保表格样式不被覆盖 */
body.liquid-glass-theme .table {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border-radius: 20px !important;
    overflow: hidden !important;
    border: 1px solid var(--glass-border) !important;
}

/* 确保下拉菜单样式不被覆盖 */
body.liquid-glass-theme .dropdown-menu {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    box-shadow: var(--glass-shadow) !important;
    padding: 0.5rem !important;
}

/* 确保模态框样式不被覆盖 */
body.liquid-glass-theme .modal-content {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur-strong) !important;
    backdrop-filter: var(--glass-blur-strong) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 20px !important;
    box-shadow: var(--glass-shadow-hover) !important;
}

/* 确保警告框样式不被覆盖 */
body.liquid-glass-theme .alert {
    background: var(--glass-bg-light) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保徽章样式不被覆盖 */
body.liquid-glass-theme .badge {
    background: var(--glass-bg-light) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保进度条样式不被覆盖 */
body.liquid-glass-theme .progress {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 25px !important;
    overflow: hidden !important;
}

/* 确保表单控件样式不被覆盖 */
body.liquid-glass-theme .form-control,
body.liquid-glass-theme .form-select {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保表单控件焦点样式不被覆盖 */
body.liquid-glass-theme .form-control:focus,
body.liquid-glass-theme .form-select:focus {
    background: var(--glass-bg-light) !important;
    border-color: var(--primary-glass) !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

/* 确保面包屑样式不被覆盖 */
body.liquid-glass-theme .breadcrumb {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    padding: 0.75rem 1rem !important;
}

/* 确保分页样式不被覆盖 */
body.liquid-glass-theme .pagination .page-link {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保分页悬停样式不被覆盖 */
body.liquid-glass-theme .pagination .page-link:hover {
    background: var(--glass-bg-light) !important;
    border-color: var(--primary-glass) !important;
    color: white !important;
}

/* 确保标签页样式不被覆盖 */
body.liquid-glass-theme .nav-tabs .nav-link {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保活动标签页样式不被覆盖 */
body.liquid-glass-theme .nav-tabs .nav-link.active {
    background: var(--primary-glass) !important;
    border-color: var(--primary-glass) !important;
    color: white !important;
}

/* 确保工具提示样式不被覆盖 */
body.liquid-glass-theme .tooltip .tooltip-inner {
    background: var(--glass-bg-dark) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保弹出框样式不被覆盖 */
body.liquid-glass-theme .popover {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
}

/* 确保手风琴样式不被覆盖 */
body.liquid-glass-theme .accordion-item {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    margin-bottom: 10px !important;
}

/* 确保手风琴按钮样式不被覆盖 */
body.liquid-glass-theme .accordion-button {
    background: transparent !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: none !important;
}

/* 确保手风琴内容样式不被覆盖 */
body.liquid-glass-theme .accordion-body {
    background: var(--glass-bg-light) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保列表组样式不被覆盖 */
body.liquid-glass-theme .list-group-item {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保列表组悬停样式不被覆盖 */
body.liquid-glass-theme .list-group-item:hover {
    background: var(--glass-bg-light) !important;
}

/* 确保轮播图样式不被覆盖 */
body.liquid-glass-theme .carousel-inner {
    border-radius: 20px !important;
    overflow: hidden !important;
}

body.liquid-glass-theme .carousel-control-prev,
body.liquid-glass-theme .carousel-control-next {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 50% !important;
    width: 50px !important;
    height: 50px !important;
}

/* 确保轮播指示器样式不被覆盖 */
body.liquid-glass-theme .carousel-indicators [data-bs-target] {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 50% !important;
}

/* 确保轮播标题样式不被覆盖 */
body.liquid-glass-theme .carousel-caption {
    background: var(--glass-bg) !important;
    -webkit-backdrop-filter: var(--glass-blur) !important;
    backdrop-filter: var(--glass-blur) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 15px !important;
    padding: 1rem !important;
}
