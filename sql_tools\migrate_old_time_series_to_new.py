#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
旧时序数据表迁移到新简化结构的脚本
将 time_series_batch 和 time_series_data 迁移到新的 time_series_data 结构
"""

import os
import sys
import logging
import json
from datetime import datetime
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, SmallInteger, DateTime, String, ARRAY, BigInteger
from sqlalchemy.dialects.postgresql import REAL
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('time_series_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TimeSeriesDataMigration:
    """时序数据迁移类"""
    
    def __init__(self):
        self.config = Config()
        self.engine = create_engine(self.config.SQLALCHEMY_DATABASE_URI)
        self.metadata = MetaData()
        
    def backup_old_tables(self) -> bool:
        """备份旧的时序数据表"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 要备份的表
            tables_to_backup = [
                ('dev_time_series_data', f'backup_dev_time_series_data_{timestamp}'),
                ('prod_time_series_data', f'backup_prod_time_series_data_{timestamp}'),
                ('dev_time_series_batch', f'backup_dev_time_series_batch_{timestamp}'),
                ('prod_time_series_batch', f'backup_prod_time_series_batch_{timestamp}'),
                ('time_series_data', f'backup_time_series_data_{timestamp}'),
                ('time_series_batch', f'backup_time_series_batch_{timestamp}')
            ]
            
            with self.engine.connect() as conn:
                for source_table, backup_table in tables_to_backup:
                    try:
                        # 检查表是否存在
                        result = conn.execute(text(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = '{source_table}'
                            );
                        """))
                        
                        if result.scalar():
                            # 创建备份表
                            conn.execute(text(f"""
                                CREATE TABLE {backup_table} AS 
                                SELECT * FROM {source_table};
                            """))
                            
                            # 获取备份的记录数
                            result = conn.execute(text(f"SELECT COUNT(*) FROM {backup_table}"))
                            count = result.scalar()
                            
                            logger.info(f"成功备份表 {source_table} 到 {backup_table}，共 {count} 条记录")
                        else:
                            logger.info(f"表 {source_table} 不存在，跳过备份")
                    
                    except Exception as e:
                        logger.warning(f"备份表 {source_table} 失败: {e}")
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"备份旧表失败: {e}")
            return False
    
    def extract_data_from_old_tables(self) -> dict:
        """从旧表中提取数据"""
        try:
            extracted_data = {
                'dev': [],
                'prod': []
            }
            
            with self.engine.connect() as conn:
                # 提取开发环境数据
                for env in ['dev', 'prod']:
                    table_name = f'{env}_time_series_data' if env in ['dev', 'prod'] else 'time_series_data'
                    
                    try:
                        # 检查表是否存在
                        result = conn.execute(text(f"""
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = '{table_name}'
                            );
                        """))
                        
                        if not result.scalar():
                            logger.info(f"表 {table_name} 不存在，跳过数据提取")
                            continue
                        
                        # 查询旧表数据
                        result = conn.execute(text(f"""
                            SELECT device_id, timestamp, data_type, data_value, 
                                   numeric_value, string_value, created_at
                            FROM {table_name}
                            ORDER BY device_id, timestamp
                        """))
                        
                        # 按设备和时间戳分组数据
                        device_data = {}
                        for row in result:
                            device_id = row[0]
                            timestamp = row[1]
                            data_type = row[2]
                            data_value = row[3]
                            numeric_value = row[4]
                            string_value = row[5]
                            created_at = row[6]
                            
                            # 创建设备时间戳的唯一键
                            key = f"{device_id}_{timestamp}"
                            
                            if key not in device_data:
                                device_data[key] = {
                                    'device_id': device_id,
                                    'timestamp': timestamp,
                                    'created_at': created_at,
                                    'data': {}
                                }
                            
                            # 解析数据值
                            value = numeric_value
                            if value is None and data_value:
                                if isinstance(data_value, dict):
                                    value = data_value.get('value')
                                elif isinstance(data_value, str):
                                    try:
                                        parsed_data = json.loads(data_value)
                                        value = parsed_data.get('value')
                                    except:
                                        pass
                            
                            if value is None and string_value:
                                try:
                                    value = float(string_value)
                                except:
                                    pass
                            
                            device_data[key]['data'][data_type] = value
                        
                        # 转换为新格式
                        for record in device_data.values():
                            new_record = self._convert_to_new_format(record)
                            if new_record:
                                extracted_data[env].append(new_record)
                        
                        logger.info(f"从 {table_name} 提取了 {len(device_data)} 条记录")
                    
                    except Exception as e:
                        logger.warning(f"从表 {table_name} 提取数据失败: {e}")
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"提取旧数据失败: {e}")
            return {'dev': [], 'prod': []}
    
    def _convert_to_new_format(self, old_record: dict) -> dict:
        """将旧格式数据转换为新格式"""
        try:
            data = old_record['data']
            
            # 提取功率通道数据
            bl0910_rms_values = []
            for i in range(1, 11):
                power_key = f'power_channel_{i}'
                if power_key in data and data[power_key] is not None:
                    # 假设功率值需要转换为RMS值，这里使用简单的转换
                    # 实际转换公式需要根据您的硬件规格调整
                    rms_value = int(data[power_key] * 10) if data[power_key] else 0
                    bl0910_rms_values.append(rms_value)
                else:
                    bl0910_rms_values.append(0)
            
            # 如果没有通道数据，尝试从power_channels获取
            if all(v == 0 for v in bl0910_rms_values) and 'power_channels' in data:
                try:
                    if isinstance(data['power_channels'], list):
                        for i, power in enumerate(data['power_channels'][:10]):
                            if power is not None:
                                bl0910_rms_values[i] = int(power * 10)
                except:
                    pass
            
            # 构建新记录
            new_record = {
                'device_id': old_record['device_id'],
                'timestamp': old_record['timestamp'],
                'created_at': old_record['created_at'],
                
                # 原始硬件数据字段
                'bl0910_error_count': int(data.get('bl0910_error_count', 0)) if data.get('bl0910_error_count') is not None else None,
                'bl0910_rms_values': bl0910_rms_values if any(v > 0 for v in bl0910_rms_values) else None,
                'relay_state': int(data.get('relay_state', 0)) if data.get('relay_state') is not None else None,
                'short_period_error_count': int(data.get('short_period_error_count', 0)) if data.get('short_period_error_count') is not None else None,
                'long_period_error_count': int(data.get('long_period_error_count', 0)) if data.get('long_period_error_count') is not None else None,
                'last_zero_cross_time': int(data.get('zero_cross_time', 0)) if data.get('zero_cross_time') is not None else None,
                
                # 转换为原始值（乘以100）
                'voltage_raw': int(data['voltage'] * 100) if data.get('voltage') is not None else None,
                'temperature_raw': int(data['temperature'] * 100) if data.get('temperature') is not None else None,
                'total_power_raw': int(data['total_power'] * 100) if data.get('total_power') is not None else None,
                
                'csq': int(data.get('csq', 0)) if data.get('csq') is not None else None,
                'ber': int(data.get('ber', 0)) if data.get('ber') is not None else None,
                'relay_pull_fault': int(data.get('relay_pull_fault', 0)) if data.get('relay_pull_fault') is not None else None,
                'relay_open_fault': int(data.get('relay_open_fault', 0)) if data.get('relay_open_fault') is not None else None,
            }
            
            # 检查是否有有效数据
            has_data = any(v is not None for k, v in new_record.items() 
                          if k not in ['device_id', 'timestamp', 'created_at'])
            
            return new_record if has_data else None
            
        except Exception as e:
            logger.warning(f"转换记录失败: {e}, 记录: {old_record}")
            return None
    
    def drop_old_tables(self) -> bool:
        """删除旧的时序数据表"""
        try:
            tables_to_drop = [
                'dev_time_series_data',
                'prod_time_series_data',
                'dev_time_series_batch',
                'prod_time_series_batch',
                'time_series_data',
                'time_series_batch'
            ]
            
            with self.engine.connect() as conn:
                for table_name in tables_to_drop:
                    try:
                        conn.execute(text(f"DROP TABLE IF EXISTS {table_name} CASCADE"))
                        logger.info(f"删除表 {table_name}")
                    except Exception as e:
                        logger.warning(f"删除表 {table_name} 失败: {e}")
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"删除旧表失败: {e}")
            return False
    
    def create_new_tables(self) -> bool:
        """创建新的简化表结构"""
        try:
            # 开发环境表
            dev_table_sql = """
            CREATE TABLE IF NOT EXISTS dev_time_series_data (
                id BIGSERIAL PRIMARY KEY,
                device_id VARCHAR(50) NOT NULL,
                timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                
                -- 原始硬件数据字段
                bl0910_error_count INTEGER,
                bl0910_rms_values INTEGER[10],
                relay_state SMALLINT,
                short_period_error_count SMALLINT,
                long_period_error_count SMALLINT,
                last_zero_cross_time INTEGER,
                voltage_raw SMALLINT,
                temperature_raw SMALLINT,
                total_power_raw INTEGER,
                csq SMALLINT,
                ber SMALLINT,
                relay_pull_fault SMALLINT,
                relay_open_fault SMALLINT,
                
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
            """
            
            # 生产环境表
            prod_table_sql = """
            CREATE TABLE IF NOT EXISTS prod_time_series_data (
                id BIGSERIAL PRIMARY KEY,
                device_id VARCHAR(50) NOT NULL,
                timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                
                -- 原始硬件数据字段
                bl0910_error_count INTEGER,
                bl0910_rms_values INTEGER[10],
                relay_state SMALLINT,
                short_period_error_count SMALLINT,
                long_period_error_count SMALLINT,
                last_zero_cross_time INTEGER,
                voltage_raw SMALLINT,
                temperature_raw SMALLINT,
                total_power_raw INTEGER,
                csq SMALLINT,
                ber SMALLINT,
                relay_pull_fault SMALLINT,
                relay_open_fault SMALLINT,
                
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );
            """
            
            with self.engine.connect() as conn:
                # 创建开发环境表
                conn.execute(text(dev_table_sql))
                logger.info("创建开发环境时序数据表")
                
                # 创建生产环境表
                conn.execute(text(prod_table_sql))
                logger.info("创建生产环境时序数据表")
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"创建新表失败: {e}")
            return False

    def create_indexes(self) -> bool:
        """创建优化索引"""
        try:
            indexes = [
                # 开发环境索引
                "CREATE INDEX IF NOT EXISTS idx_dev_device_timestamp ON dev_time_series_data(device_id, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_dev_timestamp_device ON dev_time_series_data(timestamp, device_id)",
                "CREATE INDEX IF NOT EXISTS idx_dev_voltage_range ON dev_time_series_data(voltage_raw) WHERE voltage_raw IS NOT NULL",
                "CREATE INDEX IF NOT EXISTS idx_dev_temperature_range ON dev_time_series_data(temperature_raw) WHERE temperature_raw IS NOT NULL",
                "CREATE INDEX IF NOT EXISTS idx_dev_total_power_range ON dev_time_series_data(total_power_raw) WHERE total_power_raw IS NOT NULL",

                # 生产环境索引
                "CREATE INDEX IF NOT EXISTS idx_prod_device_timestamp ON prod_time_series_data(device_id, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_prod_timestamp_device ON prod_time_series_data(timestamp, device_id)",
                "CREATE INDEX IF NOT EXISTS idx_prod_voltage_range ON prod_time_series_data(voltage_raw) WHERE voltage_raw IS NOT NULL",
                "CREATE INDEX IF NOT EXISTS idx_prod_temperature_range ON prod_time_series_data(temperature_raw) WHERE temperature_raw IS NOT NULL",
                "CREATE INDEX IF NOT EXISTS idx_prod_total_power_range ON prod_time_series_data(total_power_raw) WHERE total_power_raw IS NOT NULL",
            ]

            with self.engine.connect() as conn:
                for index_sql in indexes:
                    try:
                        conn.execute(text(index_sql))
                        logger.info(f"创建索引: {index_sql.split()[5]}")
                    except Exception as e:
                        logger.warning(f"创建索引失败: {e}")

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            return False

    def insert_migrated_data(self, extracted_data: dict) -> bool:
        """插入迁移的数据"""
        try:
            with self.engine.connect() as conn:
                for env in ['dev', 'prod']:
                    table_name = f'{env}_time_series_data'
                    data_list = extracted_data[env]

                    if not data_list:
                        logger.info(f"没有 {env} 环境的数据需要迁移")
                        continue

                    # 批量插入数据
                    insert_sql = f"""
                    INSERT INTO {table_name} (
                        device_id, timestamp, bl0910_error_count, bl0910_rms_values,
                        relay_state, short_period_error_count, long_period_error_count,
                        last_zero_cross_time, voltage_raw, temperature_raw, total_power_raw,
                        csq, ber, relay_pull_fault, relay_open_fault, created_at
                    ) VALUES (
                        :device_id, :timestamp, :bl0910_error_count, :bl0910_rms_values,
                        :relay_state, :short_period_error_count, :long_period_error_count,
                        :last_zero_cross_time, :voltage_raw, :temperature_raw, :total_power_raw,
                        :csq, :ber, :relay_pull_fault, :relay_open_fault, :created_at
                    )
                    """

                    # 分批插入，避免内存问题
                    batch_size = 1000
                    total_inserted = 0

                    for i in range(0, len(data_list), batch_size):
                        batch = data_list[i:i + batch_size]
                        conn.execute(text(insert_sql), batch)
                        total_inserted += len(batch)
                        logger.info(f"已插入 {total_inserted}/{len(data_list)} 条 {env} 环境数据")

                    logger.info(f"成功迁移 {total_inserted} 条 {env} 环境数据")

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"插入迁移数据失败: {e}")
            return False

    def run_migration(self) -> bool:
        """执行完整的迁移流程"""
        logger.info("开始时序数据迁移流程")

        # 1. 备份旧表
        logger.info("步骤1: 备份旧表")
        if not self.backup_old_tables():
            logger.error("备份旧表失败，停止迁移")
            return False

        # 2. 提取旧数据
        logger.info("步骤2: 提取旧数据")
        extracted_data = self.extract_data_from_old_tables()
        total_records = sum(len(data) for data in extracted_data.values())
        logger.info(f"提取到 {total_records} 条记录")

        if total_records == 0:
            logger.warning("没有提取到任何数据，可能旧表为空或不存在")

        # 3. 删除旧表
        logger.info("步骤3: 删除旧表")
        if not self.drop_old_tables():
            logger.error("删除旧表失败")
            return False

        # 4. 创建新表
        logger.info("步骤4: 创建新表结构")
        if not self.create_new_tables():
            logger.error("创建新表失败")
            return False

        # 5. 创建索引
        logger.info("步骤5: 创建优化索引")
        if not self.create_indexes():
            logger.warning("创建索引失败，但继续迁移")

        # 6. 插入迁移数据
        if total_records > 0:
            logger.info("步骤6: 插入迁移数据")
            if not self.insert_migrated_data(extracted_data):
                logger.error("插入迁移数据失败")
                return False

        logger.info("时序数据迁移完成")
        return True


def main():
    """主函数"""
    print("时序数据表迁移工具")
    print("=" * 50)
    print("此工具将:")
    print("1. 备份现有的 time_series_batch 和 time_series_data 表")
    print("2. 提取并转换旧数据到新格式")
    print("3. 删除旧表")
    print("4. 创建新的简化表结构")
    print("5. 插入转换后的数据")
    print("6. 创建优化索引")
    print()

    # 确认操作
    confirm = input("是否继续执行迁移？(yes/no): ").lower().strip()
    if confirm != 'yes':
        print("迁移已取消")
        return

    # 执行迁移
    migration = TimeSeriesDataMigration()

    try:
        success = migration.run_migration()

        if success:
            print("\n" + "=" * 50)
            print("✅ 迁移成功完成！")
            print("=" * 50)
            print("\n新的表结构特点:")
            print("- 只存储原始硬件数据")
            print("- 去除了复杂的JSON字段和batch表")
            print("- 优化的索引策略")
            print("- 在代码层面进行数据解析")
            print("\n请运行API测试验证新结构是否正常工作")
        else:
            print("\n" + "=" * 50)
            print("❌ 迁移失败")
            print("=" * 50)
            print("请检查日志文件 time_series_migration.log 获取详细错误信息")

    except Exception as e:
        logger.error(f"迁移过程中发生异常: {e}")
        print(f"\n❌ 迁移失败: {e}")


if __name__ == "__main__":
    main()
