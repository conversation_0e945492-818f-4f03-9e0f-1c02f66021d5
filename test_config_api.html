<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置API测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>配置API测试</h1>
        <div class="row">
            <div class="col-12">
                <button class="btn btn-primary me-2" onclick="testGetConfig()">
                    测试GET /api/config
                </button>
                <button class="btn btn-success me-2" onclick="testPostConfig()">
                    测试POST /api/config
                </button>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div id="results" class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="resultContent">点击上方按钮开始测试...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testGetConfig() {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">测试中...</span></div> 正在测试GET /api/config...';
            
            fetch('/api/config')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    resultDiv.innerHTML = `
                        <h6 class="text-success">GET /api/config 测试成功</h6>
                        <pre class="bg-light p-3">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <h6 class="text-danger">GET /api/config 测试失败</h6>
                        <div class="alert alert-danger">${error.message}</div>
                    `;
                });
        }

        function testPostConfig() {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">测试中...</span></div> 正在测试POST /api/config...';
            
            const testData = {
                products: {
                    "test123": {
                        name: "测试产品",
                        region: "cn-shanghai"
                    }
                }
            };
            
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    resultDiv.innerHTML = `
                        <h6 class="text-success">POST /api/config 测试成功</h6>
                        <pre class="bg-light p-3">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <h6 class="text-danger">POST /api/config 测试失败</h6>
                        <div class="alert alert-danger">${error.message}</div>
                    `;
                });
        }
    </script>
</body>
</html>
