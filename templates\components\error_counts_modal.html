<!-- 错误计数查询模态框 -->
<div class="modal fade" id="errorCountsModal" tabindex="-1" aria-labelledby="errorCountsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorCountsModalLabel">设备错误计数信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="errorCountsLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备错误计数信息...</p>
                </div>
                <div id="errorCountsContent" class="d-none">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">错误计数统计</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 30%;">错误计数1 (REG_ERROR_CNT1):</th>
                                                <td id="error_cnt1">--</td>
                                            </tr>
                                            <tr>
                                                <th>错误计数2 (REG_ERROR_CNT2):</th>
                                                <td id="error_cnt2">--</td>
                                            </tr>
                                            <tr>
                                                <th>错误计数3 (REG_ERROR_CNT3):</th>
                                                <td id="error_cnt3">--</td>
                                            </tr>
                                            <tr>
                                                <th>错误计数4 (REG_ERROR_CNT4):</th>
                                                <td id="error_cnt4">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>错误计数1-4</strong>: 设备运行过程中记录的各类错误事件计数</li>
                                    <li>这些计数器用于监控设备的运行状态和故障情况</li>
                                    <li>计数值越高表示对应类型的错误发生越频繁</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="errorCountsError" class="alert alert-danger d-none">
                    获取设备错误计数信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryErrorCountsModal(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备错误计数信息（模态框版本）
function queryErrorCountsModal(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('errorCountsModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('errorCountsLoading').classList.remove('d-none');
    document.getElementById('errorCountsContent').classList.add('d-none');
    document.getElementById('errorCountsError').classList.add('d-none');

    // 启动异步查询任务
    fetch(`/api/device/${window.deviceId}/async/error_counts`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 开始轮询任务状态
            pollTaskStatusForModal(data.task_id, 'error_counts', (result) => {
                // 隐藏加载中状态
                document.getElementById('errorCountsLoading').classList.add('d-none');

                if (result.success && result.error_counts) {
                    // 解析并显示错误计数信息
                    displayErrorCounts(result.error_counts);
                    // 显示内容区域
                    document.getElementById('errorCountsContent').classList.remove('d-none');
                } else {
                    throw new Error(result.error || '查询失败');
                }
            }, (error) => {
                console.error('获取设备错误计数信息失败:', error);
                // 隐藏加载中状态，显示错误信息
                document.getElementById('errorCountsLoading').classList.add('d-none');
                document.getElementById('errorCountsError').textContent = '获取设备错误计数信息失败: ' + error;
                document.getElementById('errorCountsError').classList.remove('d-none');
            });
        } else {
            throw new Error(data.error || '启动查询任务失败');
        }
    })
    .catch(error => {
        console.error('启动错误计数查询失败:', error);
        // 隐藏加载中状态，显示错误信息
        document.getElementById('errorCountsLoading').classList.add('d-none');
        document.getElementById('errorCountsError').textContent = '启动错误计数查询失败: ' + error.message;
        document.getElementById('errorCountsError').classList.remove('d-none');
    });
}

// 显示错误计数信息
function displayErrorCounts(data) {
    // 检查是否有错误计数信息
    if (!data) {
        document.getElementById('errorCountsError').textContent = '设备返回的错误计数信息格式不正确';
        document.getElementById('errorCountsError').classList.remove('d-none');
        return;
    }

    // 更新错误计数信息
    document.getElementById('error_cnt1').textContent = data.REG_ERROR_CNT1 || 0;
    document.getElementById('error_cnt2').textContent = data.REG_ERROR_CNT2 || 0;
    document.getElementById('error_cnt3').textContent = data.REG_ERROR_CNT3 || 0;
    document.getElementById('error_cnt4').textContent = data.REG_ERROR_CNT4 || 0;
}

// 任务状态轮询函数（模态框专用）
function pollTaskStatusForModal(taskId, taskType, onSuccess, onError, maxAttempts = 30) {
    let attempts = 0;

    const poll = () => {
        attempts++;

        fetch(`/api/task/${taskId}/status`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.task) {
                    const task = data.task;

                    if (task.status === 'completed') {
                        onSuccess(task.result);
                    } else if (task.status === 'failed') {
                        onError(task.error || '任务执行失败');
                    } else if (task.status === 'running' || task.status === 'pending') {
                        // 继续轮询
                        if (attempts < maxAttempts) {
                            setTimeout(poll, 1000); // 1秒后再次查询
                        } else {
                            onError('查询超时，请重试');
                        }
                    }
                } else {
                    onError('无法获取任务状态');
                }
            })
            .catch(error => {
                console.error('轮询任务状态失败:', error);
                if (attempts < maxAttempts) {
                    setTimeout(poll, 1000);
                } else {
                    onError('查询超时，请重试');
                }
            });
    };

    poll();
}
</script>
