{% extends "base.html" %}

{% block title %}无线充电发射端管理{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题和统计卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h2 class="mb-0"><i class="fas fa-broadcast-tower text-primary"></i> 无线充电发射端管理</h2>

                <!-- 操作按钮组 -->
                <div class="d-flex gap-2 flex-wrap">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransmitterModal">
                            <i class="fas fa-plus me-1"></i>添加发射端
                        </button>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#batchImportModal">
                            <i class="fas fa-file-import me-1"></i>批量导入
                        </button>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary" onclick="openHardwareVersionConfig()">
                            <i class="fas fa-cogs me-1"></i>硬件版本配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title text-primary mb-1">总发射端</h5>
                            <h3 class="mb-0" id="totalTransmitters">{{ stats.total }}</h3>
                        </div>
                        <i class="fas fa-broadcast-tower fa-2x text-primary opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title text-success mb-1">电动自行车</h5>
                            <h3 class="mb-0" id="bikeTransmitters">{{ stats.bike }}</h3>
                        </div>
                        <i class="fas fa-bicycle fa-2x text-success opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title text-warning mb-1">电动三轮车</h5>
                            <h3 class="mb-0" id="tricycleTransmitters">{{ stats.tricycle }}</h3>
                        </div>
                        <i class="fas fa-truck fa-2x text-warning opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title text-info mb-1">已绑定中控</h5>
                            <h3 class="mb-0" id="boundTransmitters">0</h3>
                        </div>
                        <i class="fas fa-link fa-2x text-info opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>搜索和筛选</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="searchInput" class="form-label">搜索</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="发射端ID、备注、主题或中控ID">
                        </div>
                        <div class="col-md-2">
                            <label for="hardwareFilter" class="form-label">硬件版本</label>
                            <select class="form-select" id="hardwareFilter">
                                <option value="all">全部</option>
                                <option value="1">电动自行车</option>
                                <option value="2">电动三轮车</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="owning_entityFilter" class="form-label">主题</label>
                            <input type="text" class="form-control" id="owning_entityFilter" placeholder="筛选主题">
                        </div>
                        <div class="col-md-2">
                            <label for="bindingFilter" class="form-label">绑定状态</label>
                            <select class="form-select" id="bindingFilter">
                                <option value="all">全部</option>
                                <option value="bound">已绑定</option>
                                <option value="unbound">未绑定</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="dateRangeFilter" class="form-label">注册日期</label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="startDateFilter" placeholder="开始日期">
                                <span class="input-group-text">至</span>
                                <input type="date" class="form-control" id="endDateFilter" placeholder="结束日期">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>清空筛选
                                </button>
                                <span class="text-muted small align-self-center">筛选条件变化时自动搜索</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发射端列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>发射端列表</h6>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="perPageSelect" style="width: auto;">
                            <option value="20">20条/页</option>
                            <option value="50">50条/页</option>
                            <option value="100">100条/页</option>
                        </select>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshTransmitterList()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="transmitterTable">
                            <thead class="table-light">
                                <tr>
                                    <th>发射端ID</th>
                                    <th>备注</th>
                                    <th>硬件版本</th>
                                    <th>软件版本</th>
                                    <th>注册日期</th>
                                    <th>绑定中控ID</th>
                                    <th>所属主体</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="transmitterTableBody">
                                <!-- 通过Ajax加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 加载状态 -->
                    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载发射端列表...</p>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="emptyState" class="text-center py-5" style="display: none;">
                        <i class="fas fa-broadcast-tower fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无发射端数据</h5>
                        <p class="text-muted">点击上方"添加发射端"按钮开始添加</p>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <span id="paginationInfo">显示 0 - 0 条，共 0 条</span>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- 分页按钮通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加发射端模态框 -->
<div class="modal fade" id="addTransmitterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加发射端</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTransmitterForm">
                    <div class="mb-3">
                        <label for="addTransmitterId" class="form-label">发射端ID <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="addTransmitterId" required>
                        <div class="form-text">请输入唯一的发射端ID</div>
                    </div>
                    <div class="mb-3">
                        <label for="addTransmitterRemark" class="form-label">备注</label>
                        <input type="text" class="form-control" id="addTransmitterRemark" placeholder="可选的备注信息">
                    </div>
                    <div class="mb-3">
                        <label for="addHardwareVersion" class="form-label">硬件版本 <span class="text-danger">*</span></label>
                        <select class="form-select" id="addHardwareVersion" required>
                            <option value="1">电动自行车</option>
                            <option value="2">电动三轮车</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="addSoftwareVersion" class="form-label">软件版本</label>
                        <input type="text" class="form-control" id="addSoftwareVersion" placeholder="例如: 1.0.0" pattern="^\d+\.\d+\.\d+$" value="0.0.0">
                        <div class="form-text">格式: major.minor.patch (例如: 1.0.0)，默认为 0.0.0</div>
                    </div>
                    <div class="mb-3">
                        <label for="addControllerId" class="form-label">绑定中控ID</label>
                        <input type="number" class="form-control" id="addControllerId" placeholder="可选的中控ID">
                    </div>
                    <div class="mb-3">
                        <label for="addTopic" class="form-label">所属主体</label>
                        <input type="text" class="form-control" id="addTopic" placeholder="可选的主题信息">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addTransmitter()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑发射端模态框 -->
<div class="modal fade" id="editTransmitterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>编辑发射端</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTransmitterForm">
                    <input type="hidden" id="editTransmitterId">
                    <div class="mb-3">
                        <label class="form-label">发射端ID</label>
                        <input type="text" class="form-control" id="editTransmitterIdDisplay" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="editTransmitterRemark" class="form-label">备注</label>
                        <input type="text" class="form-control" id="editTransmitterRemark">
                    </div>
                    <div class="mb-3">
                        <label for="editHardwareVersion" class="form-label">硬件版本</label>
                        <select class="form-select" id="editHardwareVersion">
                            <option value="1">电动自行车</option>
                            <option value="2">电动三轮车</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editSoftwareVersion" class="form-label">软件版本</label>
                        <input type="text" class="form-control" id="editSoftwareVersion" pattern="^\d+\.\d+\.\d+$">
                        <div class="form-text">格式: major.minor.patch (例如: 1.0.0)</div>
                    </div>
                    <div class="mb-3">
                        <label for="editControllerId" class="form-label">绑定中控ID</label>
                        <input type="number" class="form-control" id="editControllerId">
                    </div>
                    <div class="mb-3">
                        <label for="editTopic" class="form-label">所属主体</label>
                        <input type="text" class="form-control" id="editTopic">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateTransmitter()">
                    <i class="fas fa-save me-1"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量导入模态框 -->
<div class="modal fade" id="batchImportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-import text-success me-2"></i>批量导入发射端
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="batchImportForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">选择Excel文件</label>
                        <div class="input-group">
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".xlsx,.xls" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i> 下载模板
                            </button>
                        </div>
                        <div class="form-text">请使用Excel文件，包含发射端ID、备注、硬件版本、软件版本等字段</div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>导入说明：
                        <ul class="mb-0 mt-2">
                            <li>发射端ID为必填项，且必须唯一</li>
                            <li>已存在的发射端ID将被跳过</li>
                            <li>硬件版本：1=电动自行车，2=电动三轮车</li>
                            <li>软件版本格式：major.minor.patch (如1.0.0)，不填默认为0.0.0</li>
                            <li>其他字段为选填项</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import me-1"></i>开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导入结果模态框 -->
<div class="modal fade" id="importResultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar text-info me-2"></i>导入结果
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="text-success mb-1">成功</h5>
                                <h3 class="mb-0" id="successCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="text-warning mb-1">已存在</h5>
                                <h3 class="mb-0" id="existingCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="text-danger mb-1">失败</h5>
                                <h3 class="mb-0" id="failedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>发射端ID</th>
                                <th>状态</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody id="importResultTable">
                            <!-- 结果将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="refreshTransmitterList()">
                    <i class="fas fa-sync-alt me-1"></i>刷新列表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 硬件版本配置模态框 -->
<div class="modal fade" id="hardwareVersionConfigModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs text-secondary me-2"></i>硬件版本配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    此功能用于配置硬件版本选项，后续版本将支持动态添加硬件版本类型。
                </div>
                <div class="mb-3">
                    <label class="form-label">当前硬件版本</label>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>1</strong> - 电动自行车
                            </div>
                            <span class="badge bg-success">启用</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>2</strong> - 电动三轮车
                            </div>
                            <span class="badge bg-success">启用</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 通知组件 -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="notificationToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="fas fa-info-circle text-primary me-2"></i>
            <strong class="me-auto">系统通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="notificationMessage">
            <!-- 通知内容 -->
        </div>
    </div>
</div>

<script>
// 全局变量
let currentPage = 1;
let currentPerPage = 20;
let currentFilters = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTransmitterList();
    setupEventListeners();
});

// 初始化发射端列表
function initializeTransmitterList() {
    loadTransmitterList(1);
}

// 设置事件监听器
function setupEventListeners() {
    // 搜索框自动搜索（防抖）
    let searchTimeout;
    document.getElementById('searchInput').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 500); // 500ms防抖
    });

    // 筛选条件变化时自动搜索
    document.getElementById('hardwareFilter').addEventListener('change', applyFilters);
    document.getElementById('owning_entityFilter').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            applyFilters();
        }, 500);
    });
    document.getElementById('bindingFilter').addEventListener('change', applyFilters);
    document.getElementById('startDateFilter').addEventListener('change', applyFilters);
    document.getElementById('endDateFilter').addEventListener('change', applyFilters);

    // 每页显示数量变化
    document.getElementById('perPageSelect').addEventListener('change', function() {
        currentPerPage = parseInt(this.value);
        loadTransmitterList(1);
    });

    // 批量导入表单提交
    document.getElementById('batchImportForm').addEventListener('submit', handleBatchImport);
}

// 应用筛选条件
function applyFilters() {
    currentFilters = {
        search: document.getElementById('searchInput').value.trim(),
        hardware: document.getElementById('hardwareFilter').value,
        owning_entity: document.getElementById('owning_entityFilter').value.trim(),
        binding: document.getElementById('bindingFilter').value,
        start_date: document.getElementById('startDateFilter').value,
        end_date: document.getElementById('endDateFilter').value
    };
    loadTransmitterList(1);
}

// 清空筛选条件
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('hardwareFilter').value = 'all';
    document.getElementById('owning_entityFilter').value = '';
    document.getElementById('bindingFilter').value = 'all';
    document.getElementById('startDateFilter').value = '';
    document.getElementById('endDateFilter').value = '';
    applyFilters();
}

// 加载发射端列表
function loadTransmitterList(page = 1) {
    currentPage = page;

    // 显示加载状态
    showLoadingIndicator();

    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        per_page: currentPerPage,
        ...currentFilters
    });

    fetch(`/wireless_transmitter/api/transmitters?${params}`)
        .then(response => response.json())
        .then(data => {
            hideLoadingIndicator();
            if (data.success) {
                renderTransmitterList(data.transmitters);
                renderPagination(data.pagination);
                updateStats();
            } else {
                showNotification('加载发射端列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingIndicator();
            console.error('加载发射端列表失败:', error);
            showNotification('加载发射端列表失败', 'error');
        });
}

// 渲染发射端列表
function renderTransmitterList(transmitters) {
    const tbody = document.getElementById('transmitterTableBody');
    const emptyState = document.getElementById('emptyState');

    if (transmitters.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }

    emptyState.style.display = 'none';

    tbody.innerHTML = transmitters.map(transmitter => `
        <tr>
            <td><strong>${transmitter.transmitter_id}</strong></td>
            <td>${transmitter.transmitter_remark || '-'}</td>
            <td>
                <span class="badge ${transmitter.hardware_version === 1 ? 'bg-success' : 'bg-warning'}">
                    ${transmitter.hardware_version_name}
                </span>
            </td>
            <td>${transmitter.software_version_string}</td>
            <td>${formatDateTime(transmitter.register_date)}</td>
            <td>${transmitter.controller_id || '-'}</td>
            <td>${transmitter.owning_entity || '-'}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editTransmitter(${transmitter.transmitter_id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteTransmitter(${transmitter.transmitter_id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 渲染分页
function renderPagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    const paginationInfo = document.getElementById('paginationInfo');

    // 更新分页信息
    const start = (pagination.page - 1) * pagination.per_page + 1;
    const end = Math.min(pagination.page * pagination.per_page, pagination.total);
    paginationInfo.textContent = `显示 ${start} - ${end} 条，共 ${pagination.total} 条`;

    // 生成分页按钮
    let paginationHTML = '';

    // 上一页
    if (pagination.has_prev) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransmitterList(${pagination.page - 1})">上一页</a></li>`;
    } else {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">上一页</span></li>`;
    }

    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);

    if (startPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransmitterList(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === pagination.page) {
            paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransmitterList(${i})">${i}</a></li>`;
        }
    }

    if (endPage < pagination.pages) {
        if (endPage < pagination.pages - 1) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransmitterList(${pagination.pages})">${pagination.pages}</a></li>`;
    }

    // 下一页
    if (pagination.has_next) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransmitterList(${pagination.page + 1})">下一页</a></li>`;
    } else {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">下一页</span></li>`;
    }

    paginationElement.innerHTML = paginationHTML;
}

// 显示加载指示器
function showLoadingIndicator() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('emptyState').style.display = 'none';
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

// 刷新发射端列表
function refreshTransmitterList() {
    loadTransmitterList(currentPage);
}

// 更新统计信息
function updateStats() {
    // 这里可以添加实时统计更新逻辑
    // 暂时保持静态显示
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const toast = document.getElementById('notificationToast');
    const messageElement = document.getElementById('notificationMessage');
    const headerIcon = toast.querySelector('.toast-header i');

    messageElement.textContent = message;

    // 设置图标和样式
    headerIcon.className = `fas me-2 ${
        type === 'success' ? 'fa-check-circle text-success' :
        type === 'error' ? 'fa-exclamation-circle text-danger' :
        type === 'warning' ? 'fa-exclamation-triangle text-warning' :
        'fa-info-circle text-primary'
    }`;

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 添加发射端
function addTransmitter() {
    const form = document.getElementById('addTransmitterForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const data = {
        transmitter_id: document.getElementById('addTransmitterId').value,
        transmitter_remark: document.getElementById('addTransmitterRemark').value,
        hardware_version: document.getElementById('addHardwareVersion').value,
        software_version_string: document.getElementById('addSoftwareVersion').value,
        controller_id: document.getElementById('addControllerId').value,
        owning_entity: document.getElementById('addTopic').value
    };

    fetch('/wireless_transmitter/api/transmitters', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('发射端添加成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTransmitterModal')).hide();
            form.reset();
            loadTransmitterList(currentPage);
        } else {
            showNotification('添加失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('添加发射端失败:', error);
        showNotification('添加发射端失败', 'error');
    });
}

// 编辑发射端
function editTransmitter(transmitterId) {
    // 获取发射端信息
    fetch(`/wireless_transmitter/api/transmitters/${transmitterId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const transmitter = data.transmitter;

                // 填充编辑表单
                document.getElementById('editTransmitterId').value = transmitter.transmitter_id;
                document.getElementById('editTransmitterIdDisplay').value = transmitter.transmitter_id;
                document.getElementById('editTransmitterRemark').value = transmitter.transmitter_remark || '';
                document.getElementById('editHardwareVersion').value = transmitter.hardware_version;
                document.getElementById('editSoftwareVersion').value = transmitter.software_version_string;
                document.getElementById('editControllerId').value = transmitter.controller_id || '';
                document.getElementById('editTopic').value = transmitter.owning_entity || '';

                // 显示编辑模态框
                new bootstrap.Modal(document.getElementById('editTransmitterModal')).show();
            } else {
                showNotification('获取发射端信息失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取发射端信息失败:', error);
            showNotification('获取发射端信息失败', 'error');
        });
}

// 更新发射端
function updateTransmitter() {
    const form = document.getElementById('editTransmitterForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const transmitterId = document.getElementById('editTransmitterId').value;
    const data = {
        transmitter_remark: document.getElementById('editTransmitterRemark').value,
        hardware_version: document.getElementById('editHardwareVersion').value,
        software_version_string: document.getElementById('editSoftwareVersion').value,
        controller_id: document.getElementById('editControllerId').value,
        owning_entity: document.getElementById('editTopic').value
    };

    fetch(`/wireless_transmitter/api/transmitters/${transmitterId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('发射端更新成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editTransmitterModal')).hide();
            loadTransmitterList(currentPage);
        } else {
            showNotification('更新失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新发射端失败:', error);
        showNotification('更新发射端失败', 'error');
    });
}

// 删除发射端
function deleteTransmitter(transmitterId) {
    if (!confirm(`确定要删除发射端 ${transmitterId} 吗？此操作不可恢复。`)) {
        return;
    }

    fetch(`/wireless_transmitter/api/transmitters/${transmitterId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('发射端删除成功', 'success');
            loadTransmitterList(currentPage);
        } else {
            showNotification('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除发射端失败:', error);
        showNotification('删除发射端失败', 'error');
    });
}

// 处理批量导入
function handleBatchImport(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    // 显示加载状态
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导入中...';
    submitBtn.disabled = true;

    fetch('/wireless_transmitter/api/batch_import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (data.success !== undefined) {
            // 显示导入结果
            document.getElementById('successCount').textContent = data.success_count || 0;
            document.getElementById('existingCount').textContent = data.existing_count || 0;
            document.getElementById('failedCount').textContent = data.failed_count || 0;

            // 填充结果表格
            const resultTable = document.getElementById('importResultTable');
            resultTable.innerHTML = '';

            if (data.results) {
                data.results.forEach(result => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${result.transmitter_id}</td>
                        <td>
                            <span class="badge ${result.status === 'success' ? 'bg-success' :
                                              result.status === 'existing' ? 'bg-warning' : 'bg-danger'}">
                                ${result.status === 'success' ? '成功' :
                                  result.status === 'existing' ? '已存在' : '失败'}
                            </span>
                        </td>
                        <td>${result.message}</td>
                    `;
                    resultTable.appendChild(row);
                });
            }

            // 关闭导入模态框，显示结果模态框
            const importModal = bootstrap.Modal.getInstance(document.getElementById('batchImportModal'));
            importModal.hide();
            const resultModal = new bootstrap.Modal(document.getElementById('importResultModal'));
            resultModal.show();
        } else {
            showNotification('导入失败: ' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.error('批量导入失败:', error);
        showNotification('批量导入失败', 'error');
    });
}

// 下载模板
function downloadTemplate() {
    const link = document.createElement('a');
    link.href = '/wireless_transmitter/api/download_template';
    link.download = '无线充电发射端导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 打开硬件版本配置
function openHardwareVersionConfig() {
    const modal = new bootstrap.Modal(document.getElementById('hardwareVersionConfigModal'));
    modal.show();
}
</script>
{% endblock %}
