<!-- SIM卡信息查询模态框 -->
<div class="modal fade" id="simInfoModal" tabindex="-1" aria-labelledby="simInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="simInfoModalLabel">设备SIM卡信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="simInfoLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备SIM卡信息...</p>
                </div>
                <div id="simInfoContent" class="d-none">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">SIM卡信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 30%;">IMEI:</th>
                                                <td id="sim_imei">--</td>
                                            </tr>
                                            <tr>
                                                <th>IMEI长度:</th>
                                                <td id="sim_imei_len">--</td>
                                            </tr>
                                            <tr>
                                                <th>ICCID:</th>
                                                <td id="sim_iccid">--</td>
                                            </tr>
                                            <tr>
                                                <th>ICCID长度:</th>
                                                <td id="sim_iccid_len">--</td>
                                            </tr>
                                            <tr>
                                                <th>查询类型:</th>
                                                <td id="sim_query_type">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>IMEI</strong>: 国际移动设备识别码，用于唯一标识移动设备</li>
                                    <li><strong>ICCID</strong>: 集成电路卡识别码，SIM卡的唯一标识符</li>
                                    <li>这些信息用于设备的网络连接和身份验证</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="simInfoError" class="alert alert-danger d-none">
                    获取设备SIM卡信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="querySimInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备SIM卡信息
function querySimInfo(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('simInfoModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('simInfoLoading').classList.remove('d-none');
    document.getElementById('simInfoContent').classList.add('d-none');
    document.getElementById('simInfoError').classList.add('d-none');

    // 发送请求获取设备SIM卡信息
    fetch(`/api/device/${window.deviceId}/sim_card_info`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('simInfoLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('simInfoError').textContent = '获取设备SIM卡信息失败: ' + data.error;
                document.getElementById('simInfoError').classList.remove('d-none');
                return;
            }

            // 解析并显示SIM卡信息
            displaySimInfo(data);

            // 显示内容区域
            document.getElementById('simInfoContent').classList.remove('d-none');
        })
        .catch(error => {
            console.error('获取设备SIM卡信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('simInfoLoading').classList.add('d-none');
            document.getElementById('simInfoError').textContent = '获取设备SIM卡信息失败: ' + error.message;
            document.getElementById('simInfoError').classList.remove('d-none');
        });
}

// 显示SIM卡信息
function displaySimInfo(data) {
    // 检查是否有SIM卡信息
    if (!data) {
        document.getElementById('simInfoError').textContent = '设备返回的SIM卡信息格式不正确';
        document.getElementById('simInfoError').classList.remove('d-none');
        return;
    }

    // 更新SIM卡信息
    document.getElementById('sim_imei').textContent = data.imei || '--';
    document.getElementById('sim_imei_len').textContent = data.imei_len || 0;
    document.getElementById('sim_iccid').textContent = data.iccid || '--';
    document.getElementById('sim_iccid_len').textContent = data.iccid_len || 0;
    
    // 查询类型映射
    const queryTypeMap = {
        0: '设备调试信息',
        1: '电流RMS信息', 
        2: 'SIM卡信息'
    };
    document.getElementById('sim_query_type').textContent = queryTypeMap[data.query_type] || data.query_type || '--';
}
</script>
