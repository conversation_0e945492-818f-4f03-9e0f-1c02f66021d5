{% extends "base.html" %}

{% block title %}设备参数 - {{ device.device_id }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-microchip text-primary me-2" style="font-size: 1.5rem;"></i>
                            <h5 class="mb-0">设备参数 - {{ device.device_id }}</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge rounded-pill bg-primary-subtle text-primary me-2">
                                <i class="fas fa-info-circle me-1"></i> 设备参数: <span id="param-count">9999</span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body pb-0">
                    <!-- 操作按钮区域 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-primary" onclick="queryParameters()">
                                    <i class="fas fa-sync-alt me-1"></i> 查询参数
                                </button>
                                <button class="btn btn-secondary" onclick="queryDebugInfo()">
                                    <i class="fas fa-bug me-1"></i> 查询调试信息
                                </button>
                                <button class="btn btn-info" onclick="queryFirmwareInfo()">
                                    <i class="fas fa-microchip me-1"></i> 查询固件信息
                                </button>
                                <button class="btn btn-warning" onclick="queryErrorCountsModal()">
                                    <i class="fas fa-exclamation-triangle me-1"></i> 查询错误计数
                                </button>
                                <button class="btn btn-info" onclick="queryDeviceLocationModal()">
                                    <i class="fas fa-map-marker-alt me-1"></i> 查询位置
                                </button>
                                <button class="btn btn-success" onclick="openDebugScriptModal()">
                                    <i class="fas fa-code me-1"></i> 调试脚本
                                </button>
                                <button class="btn btn-success" onclick="querySimInfo()">
                                    <i class="fas fa-sim-card me-1"></i> 查询SIM卡信息
                                </button>
                                <button class="btn btn-primary" onclick="queryCurrentRmsInfo()">
                                    <i class="fas fa-chart-line me-1"></i> 查询电流RMS
                                </button>
                                <button class="btn btn-secondary" onclick="queryWirelessChargerInfo()">
                                    <i class="fas fa-wifi me-1"></i> 无线充电映射
                                </button>
                                <button class="btn btn-warning" onclick="queryWtcInfo()">
                                    <i class="fas fa-bolt me-1"></i> 无线充电信息
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                                <button class="btn btn-success me-2" id="exportParametersBtn">
                                    <i class="fas fa-download me-1"></i> 导出参数
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="historyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-chart-line me-1"></i> 历史数据
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="historyDropdown">
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.power_history_page', device_id=device.id) }}">
                                                <i class="fas fa-bolt me-2 text-warning"></i> 功率
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.temperature_history_page', device_id=device.id) }}">
                                                <i class="fas fa-thermometer-half me-2 text-danger"></i> 温度
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.voltage_history_page', device_id=device.id) }}">
                                                <i class="fas fa-plug me-2 text-info"></i> 电压
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('debug_script.csq_history_page', device_id=device.id) }}">
                                                <i class="fas fa-signal me-2 text-success"></i> 信号质量
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 参数分类标签 -->
                    <div class="mb-3">
                        <ul class="nav nav-pills" id="parameterTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                                    <i class="fas fa-list me-1"></i> 全部
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="time-tab" data-bs-toggle="pill" data-bs-target="#time" type="button" role="tab" aria-controls="time" aria-selected="false">
                                    <i class="fas fa-clock me-1"></i> 时间参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="power-tab" data-bs-toggle="pill" data-bs-target="#power" type="button" role="tab" aria-controls="power" aria-selected="false">
                                    <i class="fas fa-bolt me-1"></i> 功率参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="other-tab" data-bs-toggle="pill" data-bs-target="#other" type="button" role="tab" aria-controls="other" aria-selected="false">
                                    <i class="fas fa-sliders-h me-1"></i> 其他参数
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- 参数表格 -->
                    <div class="tab-content" id="parameterTabsContent">
                        <!-- 全部参数 -->
                        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for param in parameters %}
                                        <tr>
                                            <td>{{ param.name }}</td>
                                            <td>{{ param.address }}</td>
                                            <td id="{{ param.name }}">{{ param.value if param.value != None else '--' }}</td>
                                            <td>{{ param.description or '' }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="editParameter('{{ param.name }}', {{ param.address }})">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}

                            </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 时间参数 -->
                        <div class="tab-pane fade" id="time" role="tabpanel" aria-labelledby="time-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="time-params">
                                        <!-- 时间参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 功率参数 -->
                        <div class="tab-pane fade" id="power" role="tabpanel" aria-labelledby="power-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="power-params">
                                        <!-- 功率参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 其他参数 -->
                        <div class="tab-pane fade" id="other" role="tabpanel" aria-labelledby="other-tab">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>参数名称</th>
                                            <th>寄存器地址</th>
                                            <th>参数值</th>
                                            <th>参数说明</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="other-params">
                                        <!-- 其他参数将通过JavaScript动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑参数模态框 -->
<div class="modal fade" id="editParameterModal" tabindex="-1" aria-labelledby="editParameterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editParameterModalLabel">编辑参数</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editParameterForm">
                    <input type="hidden" id="paramName">
                    <input type="hidden" id="paramAddr">
                    <div class="mb-3">
                        <label class="form-label fw-bold" id="paramNameLabel">参数名称</label>
                        <p class="text-muted mb-3" id="paramDescription">参数描述</p>
                        <label for="paramValue" class="form-label">参数值</label>
                        <input type="number" class="form-control" id="paramValue" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveParameter()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 设备调试信息模态框 -->
<div class="modal fade" id="debugInfoModal" tabindex="-1" aria-labelledby="debugInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="debugInfoModalLabel">设备调试信息 - {{ device.device_id }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="debugInfoLoading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备调试信息，请稍候...</p>
                </div>
                <div id="debugInfoContent" class="d-none">
                    <!-- 调试信息内容将通过JavaScript动态填充 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">错误计数</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>BL0910错误计数:</th>
                                                <td id="bl0910_error_count">--</td>
                                            </tr>
                                            <tr>
                                                <th>短周期错误计数:</th>
                                                <td id="short_period_error_count">--</td>
                                            </tr>
                                            <tr>
                                                <th>长周期错误计数:</th>
                                                <td id="long_period_error_count">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">继电器状态</h6>
                                </div>
                                <div class="card-body">
                                    <div id="relay_state_value" class="mb-2">继电器状态值: --</div>
                                    <div class="row" id="relay_bits_container">
                                        <!-- 继电器状态位将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">BL0910 RMS寄存器值</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>通道</th>
                                                    <th>寄存器值</th>
                                                    <th>计算功率 (W)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bl0910_rms_regs_container">
                                                <!-- BL0910 RMS寄存器值将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">其他信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>零交叉时间:</th>
                                                <td id="zero_cross_time">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">传感器数据</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th>电压有效值:</th>
                                                <td id="voltage_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>温度:</th>
                                                <td id="temperature_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>总有功功率:</th>
                                                <td id="total_power_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>信号质量 (CSQ):</th>
                                                <td id="csq_value">--</td>
                                            </tr>
                                            <tr>
                                                <th>误码率 (BER):</th>
                                                <td id="ber_value">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div id="debugInfoError" class="alert alert-danger d-none">
                    获取设备调试信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryDebugInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>



<!-- 引入调试脚本模态框 -->
{% include 'components/debug_script_modal.html' %}

<!-- 引入固件信息查询模态框 -->
{% include 'components/firmware_info_modal.html' %}

<!-- 引入SIM卡信息查询模态框 -->
{% include 'components/sim_info_modal.html' %}

<!-- 引入错误计数查询模态框 -->
{% include 'components/error_counts_modal.html' %}

<!-- 引入设备位置查询模态框 -->
{% include 'components/device_location_modal.html' %}

<!-- 引入电流RMS信息查询模态框 -->
{% include 'components/current_rms_modal.html' %}

<!-- 引入无线充电映射表查询模态框 -->
{% include 'components/wireless_charger_modal.html' %}

<!-- 引入无线充电信息查询模态框 -->
{% include 'components/wtc_info_modal.html' %}
{% endblock %}

{% block scripts %}
<script>
    // 设置全局设备ID，供模态框组件使用
    window.deviceId = {{ device.id }};
    // BL0910 功率计算系数 [斜率, 截距]
    const BL0910_ACTIVE_POWER_COEF = [
        [-0.0014, -0.8803],  /* MEASURE_1 */
        [-0.0014, -0.6832],  /* MEASURE_2 */
        [0.0014, -0.726],    /* MEASURE_3 */
        [0.0014, -0.6832],   /* MEASURE_4 */
        [0.0014, -0.6832],   /* MEASURE_5 */
        [0.00134339, -0.8044], /* MEASURE_6 */
        [0.00134339, -0.8044], /* MEASURE_7 */
        [-0.0014, -0.6832],  /* MEASURE_8 */
        [-0.0014, -0.6832],  /* MEASURE_9 */
        [0.0014, -0.8305]    /* MEASURE_10 */
    ];

    // 计算BL0910寄存器值对应的功率
    function calculatePowerFromRegister(registerValue, channelIndex) {
        // 将寄存器值视为int32_t整数
        // JavaScript中没有int32_t类型，但可以通过位运算模拟
        const int32Value = registerValue | 0; // 强制转换为32位有符号整数

        // 获取对应通道的系数
        const coef = BL0910_ACTIVE_POWER_COEF[channelIndex];

        // 应用线性变换: power = slope * register_value + intercept
        const power = coef[0] * int32Value + coef[1];

        // 返回计算结果，保留2位小数
        return power.toFixed(2);
    }

    // 从后端获取的参数数据
    const savedParameterData = {{ parameter_data | tojson | safe }};

    // 动态获取参数分类
    function getParametersByCategory(category) {
        const params = [];
        if (savedParameterData) {
            Object.keys(savedParameterData).forEach(paramName => {
                const paramData = savedParameterData[paramName];
                if (paramData.category === category) {
                    params.push(paramName);
                }
            });
        }
        return params;
    }

    // 获取各类参数
    const timeParams = getParametersByCategory('time');
    const powerParams = getParametersByCategory('power');

    // 页面加载时自动获取参数
    document.addEventListener('DOMContentLoaded', function() {
        // 首先尝试从数据库获取保存的参数
        fetchSavedParameters();

        // 初始化参数分类
        initializeParameterCategories();

        // 更新参数计数
        updateParameterCount();

        // 添加标签切换事件监听
        document.querySelectorAll('#parameterTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(event) {
                // 当标签被激活时，确保对应的参数表格是最新的
                updateParameterCategories();
            });
        });
    });

    // 初始化参数分类
    function initializeParameterCategories() {
        // 获取所有参数行
        const allRows = document.querySelectorAll('#all tbody tr');

        // 清空分类表格
        document.querySelector('.time-params').innerHTML = '';
        document.querySelector('.power-params').innerHTML = '';
        document.querySelector('.other-params').innerHTML = '';

        // 分类并复制参数行到对应的表格
        allRows.forEach(row => {
            const paramName = row.querySelector('td:first-child').textContent;
            const clonedRow = row.cloneNode(true);

            // 从数据库数据中获取参数分类
            let category = 'other'; // 默认分类
            if (savedParameterData && savedParameterData[paramName]) {
                category = savedParameterData[paramName].category || 'other';
            }

            // 根据分类添加到对应的表格
            if (category === 'time') {
                document.querySelector('.time-params').appendChild(clonedRow);
            } else if (category === 'power') {
                document.querySelector('.power-params').appendChild(clonedRow);
            } else {
                document.querySelector('.other-params').appendChild(clonedRow);
            }
        });
    }

    // 更新参数分类
    function updateParameterCategories() {
        initializeParameterCategories();
    }

    // 更新参数计数
    function updateParameterCount() {
        const allParams = document.querySelectorAll('#all tbody tr').length;
        document.getElementById('param-count').textContent = allParams;
    }

    // 从数据库获取保存的参数
    function fetchSavedParameters() {
        // 首先使用后端传递的数据
        if (savedParameterData && Object.keys(savedParameterData).length > 0) {
            // 更新参数值
            for (const [key, paramData] of Object.entries(savedParameterData)) {
                const elements = document.querySelectorAll(`[id="${key}"]`);
                elements.forEach(element => {
                    if (element) {
                        if (typeof paramData === 'object' && paramData !== null && 'value' in paramData) {
                            element.textContent = paramData.value;
                        } else {
                            element.textContent = paramData;
                        }
                    }
                });
            }

            // 更新参数分类
            updateParameterCategories();
            return;
        }

        // 如果没有后端数据，则通过API获取
        // 显示加载中
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '--';
        });

        // 发送请求获取保存的参数
        fetch('/api/device/{{ device.id }}/saved_parameters')
            .then(response => response.json())
            .then(data => {
                // 更新参数值
                for (const [key, paramData] of Object.entries(data)) {
                    const elements = document.querySelectorAll(`[id="${key}"]`);
                    elements.forEach(element => {
                        if (element) {
                            if (typeof paramData === 'object' && paramData !== null && 'value' in paramData) {
                                element.textContent = paramData.value;
                            } else {
                                element.textContent = paramData;
                            }
                        }
                    });
                }

                // 更新参数分类
                updateParameterCategories();
            })
            .catch(error => {
                console.error('获取保存的参数失败:', error);
                // 如果获取保存的参数失败，恢复默认显示
                paramElements.forEach(el => {
                    el.textContent = '--';
                });
            });
    }

    // 查询设备位置（保留原有的alert版本，用于兼容性）
    function queryDeviceLocation() {
        // 显示加载中提示
        const locationBtn = document.querySelector('button[onclick="queryDeviceLocationModal()"]');
        if (locationBtn) {
            const originalText = locationBtn.innerHTML;
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
            locationBtn.disabled = true;

            // 发送请求获取设备位置
            fetch('/api/device/{{ device.id }}/location')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('获取设备位置失败: ' + data.error);
                    } else if (data.success && data.location) {
                        const location = data.location;
                        alert(`设备位置查询成功:\n位置代码: ${location.location_code}\n纬度: ${location.latitude}\n经度: ${location.longitude}`);
                    } else {
                        alert('设备位置查询成功，已更新到数据库');
                    }
                })
                .catch(error => {
                    console.error('获取设备位置失败:', error);
                    alert('获取设备位置失败，请重试');
                })
                .finally(() => {
                    // 恢复按钮状态
                    locationBtn.innerHTML = originalText;
                    locationBtn.disabled = false;
                });
        }
    }

    // 查询参数（异步版本）
    function queryParameters() {
        // 显示加载中
        const paramElements = document.querySelectorAll('[id^="REG_"]');
        paramElements.forEach(el => {
            el.textContent = '查询中...';
        });

        // 启动异步查询任务
        fetch('/api/device/{{ device.id }}/async/parameters', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, 'parameters', (result) => {
                    if (result.success && result.parameters) {
                        // 遍历返回的数据
                        Object.entries(result.parameters).forEach(([paramName, paramData]) => {
                            // 查找对应的元素
                            const element = document.getElementById(paramName);
                            if (element && paramData && typeof paramData === 'object' && 'value' in paramData) {
                                element.textContent = paramData.value;
                            }
                        });
                        // 更新参数分类
                        updateParameterCategories();
                        showNotification('参数查询完成', 'success');
                    } else {
                        throw new Error(result.error || '查询失败');
                    }
                }, (error) => {
                    console.error('获取参数失败:', error);
                    showNotification('获取参数失败: ' + error, 'error');
                    // 恢复默认显示
                    paramElements.forEach(el => {
                        el.textContent = '--';
                    });
                });
            } else {
                throw new Error(data.error || '启动查询任务失败');
            }
        })
        .catch(error => {
            console.error('启动参数查询失败:', error);
            showNotification('启动参数查询失败: ' + error, 'error');
            // 恢复默认显示
            paramElements.forEach(el => {
                el.textContent = '--';
            });
        });
    }

    // 任务状态轮询函数
    function pollTaskStatus(taskId, taskType, onSuccess, onError, maxAttempts = 30) {
        let attempts = 0;

        const poll = () => {
            attempts++;

            fetch(`/api/task/${taskId}/status`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.task) {
                        const task = data.task;

                        if (task.status === 'completed') {
                            onSuccess(task.result);
                        } else if (task.status === 'failed') {
                            onError(task.error || '任务执行失败');
                        } else if (task.status === 'running' || task.status === 'pending') {
                            // 继续轮询
                            if (attempts < maxAttempts) {
                                setTimeout(poll, 1000); // 1秒后再次查询
                            } else {
                                onError('查询超时，请重试');
                            }
                        }
                    } else {
                        onError('无法获取任务状态');
                    }
                })
                .catch(error => {
                    console.error('轮询任务状态失败:', error);
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 1000);
                    } else {
                        onError('查询超时，请重试');
                    }
                });
        };

        poll();
    }

    // 显示通知函数
    function showNotification(message, type = 'info') {
        // 简单的通知实现，可以根据需要替换为更复杂的通知系统
        if (type === 'success') {
            console.log('✓ ' + message);
        } else if (type === 'error') {
            console.error('✗ ' + message);
            alert('错误: ' + message);
        } else {
            console.info('ℹ ' + message);
        }
    }

    // 编辑参数
    function editParameter(paramName, paramAddr) {
        const currentValue = document.getElementById(paramName).textContent;
        document.getElementById('paramName').value = paramName;
        document.getElementById('paramAddr').value = paramAddr;
        document.getElementById('paramValue').value = currentValue !== '--' ? currentValue : '';

        // 设置参数名称和描述
        document.getElementById('paramNameLabel').textContent = paramName;

        // 获取参数描述
        const paramRow = document.querySelector(`tr:has(button[onclick="editParameter('${paramName}', ${paramAddr})"])`);
        const paramDescription = paramRow.querySelector('td:nth-child(4)').textContent;
        document.getElementById('paramDescription').textContent = paramDescription;

        // 更新模态框标题
        document.getElementById('editParameterModalLabel').textContent = `编辑参数 - ${paramName}`;

        const modal = new bootstrap.Modal(document.getElementById('editParameterModal'));
        modal.show();
    }

    // 保存参数
    function saveParameter() {
        const paramName = document.getElementById('paramName').value;
        const paramAddr = document.getElementById('paramAddr').value;
        const paramValue = document.getElementById('paramValue').value;

        if (!paramValue) {
            alert('请输入参数值');
            return;
        }

        // 发送请求保存参数
        fetch('/api/device/{{ device.id }}/parameters', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reg_addr: paramAddr,
                reg_value: paramValue
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新所有相同ID的元素显示
                const elements = document.querySelectorAll(`[id="${paramName}"]`);
                elements.forEach(element => {
                    element.textContent = paramValue;
                });

                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('editParameterModal')).hide();
                alert('参数保存成功');
            } else {
                alert('参数保存失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('保存参数失败:', error);
            alert('保存参数失败，请重试');
        });
    }

    // 查询错误计数（异步版本）
    function queryErrorCounts() {
        // 显示加载中提示
        const errorBtn = document.querySelector('button[onclick="queryErrorCounts()"]');
        const originalText = errorBtn.innerHTML;
        errorBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        errorBtn.disabled = true;

        // 启动异步查询任务
        fetch('/api/device/{{ device.id }}/async/error_counts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, 'error_counts', (result) => {
                    if (result.success && result.error_counts) {
                        const counts = result.error_counts;
                        let message = '错误计数查询结果：\n\n';
                        message += `错误计数1 (REG_ERROR_CNT1): ${counts.REG_ERROR_CNT1 || 0}\n`;
                        message += `错误计数2 (REG_ERROR_CNT2): ${counts.REG_ERROR_CNT2 || 0}\n`;
                        message += `错误计数3 (REG_ERROR_CNT3): ${counts.REG_ERROR_CNT3 || 0}\n`;
                        message += `错误计数4 (REG_ERROR_CNT4): ${counts.REG_ERROR_CNT4 || 0}`;
                        if (result.note) {
                            message += '\n\n注意: ' + result.note;
                        }
                        alert(message);
                        showNotification('错误计数查询完成', 'success');
                    } else {
                        throw new Error(result.error || '查询失败');
                    }
                    // 恢复按钮状态
                    errorBtn.innerHTML = originalText;
                    errorBtn.disabled = false;
                }, (error) => {
                    console.error('获取错误计数失败:', error);
                    showNotification('获取错误计数失败: ' + error, 'error');
                    // 恢复按钮状态
                    errorBtn.innerHTML = originalText;
                    errorBtn.disabled = false;
                });
            } else {
                throw new Error(data.error || '启动查询任务失败');
            }
        })
        .catch(error => {
            console.error('启动错误计数查询失败:', error);
            showNotification('启动错误计数查询失败: ' + error, 'error');
            // 恢复按钮状态
            errorBtn.innerHTML = originalText;
            errorBtn.disabled = false;
        });
    }



    // 查询设备调试信息（异步版本）
    function queryDebugInfo(isRefresh = false) {
        // 显示模态框
        if (!isRefresh) {
            const modal = new bootstrap.Modal(document.getElementById('debugInfoModal'));
            modal.show();
        }

        // 显示加载中状态
        document.getElementById('debugInfoLoading').classList.remove('d-none');
        document.getElementById('debugInfoContent').classList.add('d-none');
        document.getElementById('debugInfoError').classList.add('d-none');

        // 启动异步查询任务
        fetch('/api/device/{{ device.id }}/async/debug_info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, 'debug_info', (result) => {
                    // 隐藏加载中状态
                    document.getElementById('debugInfoLoading').classList.add('d-none');

                    if (result.success && result.debug_info) {
                        // 解析并显示调试信息
                        displayDebugInfo(result.debug_info);
                        // 显示内容区域
                        document.getElementById('debugInfoContent').classList.remove('d-none');
                        showNotification('调试信息查询完成', 'success');
                    } else {
                        throw new Error(result.error || '查询失败');
                    }
                }, (error) => {
                    console.error('获取设备调试信息失败:', error);
                    // 隐藏加载中状态，显示错误信息
                    document.getElementById('debugInfoLoading').classList.add('d-none');
                    document.getElementById('debugInfoError').textContent = '获取设备调试信息失败: ' + error;
                    document.getElementById('debugInfoError').classList.remove('d-none');
                });
            } else {
                throw new Error(data.error || '启动查询任务失败');
            }
        })
        .catch(error => {
            console.error('启动调试信息查询失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('debugInfoLoading').classList.add('d-none');
            document.getElementById('debugInfoError').textContent = '启动调试信息查询失败: ' + error.message;
            document.getElementById('debugInfoError').classList.remove('d-none');
        });
    }

    // 显示调试信息
    function displayDebugInfo(data) {
        // 检查是否有调试信息
        if (!data) {
            document.getElementById('debugInfoError').textContent = '设备返回的调试信息格式不正确';
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        // 根据实际返回的数据结构进行处理
        // 数据格式: {'session_id': 1, 'result': 0, 'info': {...}}
        if (data.result !== 0) {
            document.getElementById('debugInfoError').textContent = '设备返回错误: ' + data.result;
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        if (!data.info) {
            document.getElementById('debugInfoError').textContent = '设备返回的调试信息不包含info字段';
            document.getElementById('debugInfoError').classList.remove('d-none');
            return;
        }

        const info = data.info;

        // 更新错误计数
        document.getElementById('bl0910_error_count').textContent = info.bl0910_error_count || '0';
        document.getElementById('short_period_error_count').textContent = info.short_period_error_count || '0';
        document.getElementById('long_period_error_count').textContent = info.long_period_error_count || '0';

        // 更新继电器状态
        document.getElementById('relay_state_value').textContent = `继电器状态值: ${info.relay_state || '0'} (0x${(info.relay_state || 0).toString(16).toUpperCase().padStart(4, '0')})`;

        // 更新继电器状态位
        const relayBitsContainer = document.getElementById('relay_bits_container');
        relayBitsContainer.innerHTML = '';

        // 如果有继电器状态位信息，使用它
        if (info.relay_bits) {
            for (let i = 0; i < 16; i++) {
                const bitName = `relay_${i}`;
                const bitValue = info.relay_bits[bitName];
                const bitStatus = bitValue ? '开启' : '关闭';
                const bitClass = bitValue ? 'success' : 'secondary';

                const bitElement = document.createElement('div');
                bitElement.className = 'col-md-3 col-6 mb-2';
                bitElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-${bitClass} me-2">${i}</span>
                        <span>${bitStatus}</span>
                    </div>
                `;
                relayBitsContainer.appendChild(bitElement);
            }
        } else {
            // 如果没有继电器状态位信息，但有继电器状态值，则手动计算
            const relayState = info.relay_state || 0;
            for (let i = 0; i < 16; i++) {
                const bitValue = (relayState & (1 << i)) !== 0;
                const bitStatus = bitValue ? '开启' : '关闭';
                const bitClass = bitValue ? 'success' : 'secondary';

                const bitElement = document.createElement('div');
                bitElement.className = 'col-md-3 col-6 mb-2';
                bitElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-${bitClass} me-2">${i}</span>
                        <span>${bitStatus}</span>
                    </div>
                `;
                relayBitsContainer.appendChild(bitElement);
            }
        }

        // 更新BL0910 RMS寄存器值
        const rmsRegsContainer = document.getElementById('bl0910_rms_regs_container');
        rmsRegsContainer.innerHTML = '';

        if (info.bl0910_rms_regs && Array.isArray(info.bl0910_rms_regs)) {
            info.bl0910_rms_regs.forEach((value, index) => {
                // 计算功率值
                const powerValue = calculatePowerFromRegister(value, index);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>通道 ${index + 1}</td>
                    <td>${value} (0x${value.toString(16).toUpperCase().padStart(8, '0')})</td>
                    <td>${powerValue} W</td>
                `;
                rmsRegsContainer.appendChild(row);
            });
        }

        // 更新零交叉时间
        document.getElementById('zero_cross_time').textContent = info.zero_cross_time ? `${info.zero_cross_time} ms` : '--';

        // 更新新增的传感器数据
        if (info.voltage !== undefined) {
            document.getElementById('voltage_value').textContent = `${(info.voltage / 100).toFixed(2)} V`;
        } else {
            document.getElementById('voltage_value').textContent = '--';
        }

        if (info.temperature !== undefined) {
            document.getElementById('temperature_value').textContent = `${(info.temperature / 100).toFixed(2)} °C`;
        } else {
            document.getElementById('temperature_value').textContent = '--';
        }

        if (info.total_power !== undefined) {
            document.getElementById('total_power_value').textContent = `${(info.total_power / 100).toFixed(2)} W`;
        } else {
            document.getElementById('total_power_value').textContent = '--';
        }

        if (info.csq !== undefined) {
            let csqQuality = '';
            if (info.csq >= 20) csqQuality = '(优)';
            else if (info.csq >= 15) csqQuality = '(良)';
            else if (info.csq >= 10) csqQuality = '(中)';
            else csqQuality = '(差)';

            // 计算dBm值
            let dBm = '';
            if (info.csq === 0) dBm = '≤-115dBm';
            else if (info.csq === 1) dBm = '-111dBm';
            else if (info.csq >= 2 && info.csq <= 30) dBm = `-${113 - (info.csq*2)}dBm`;
            else if (info.csq === 31) dBm = '≥-51dBm';
            else if (info.csq === 99) dBm = '未知';

            document.getElementById('csq_value').textContent = `${info.csq} ${csqQuality} [${dBm}]`;
        } else {
            document.getElementById('csq_value').textContent = '--';
        }

        if (info.ber !== undefined) {
            let berText = '';
            if (info.ber >= 0 && info.ber <= 7) {
                berText = `${info.ber} (RXQUAL值)`;
            } else if (info.ber === 99) {
                berText = `${info.ber} (未知或不可测)`;
            } else {
                berText = `${info.ber}`;
            }
            document.getElementById('ber_value').textContent = berText;
        } else {
            document.getElementById('ber_value').textContent = '--';
        }
    }

    // 打开调试脚本模态框
    function openDebugScriptModal() {
        const modal = new bootstrap.Modal(document.getElementById('debugScriptModal'));
        modal.show();
    }

    function querySimInfo() {
        // 显示加载中提示
        const errorBtn = document.querySelector('button[onclick="querySimInfo()"]');
        const originalText = errorBtn.innerHTML;
        errorBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
        errorBtn.disabled = true;

        // 发送请求获取SIM卡信息
        fetch('/api/device/{{ device.id }}/sim_card_info')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('获取错误SIM信息失败: ' + data.error);
                } else {
                    let message = 'SIM信息查询结果：\n\n';
                    message += `IMEI 长度 (IMEI_LEN): ${data.imei_len || '未知'}\n`;
                    message += `IMEI (IMEI): ${data.imei || '未知'}\n`;
                    message += `ICCID 长度 (ICCID_LEN): ${data.iccid_len || '未知'}\n`;
                    message += `ICCID (ICCID): ${data.iccid || '未知'}\n`;
                    message += `查询类型 (QUERY_TYPE): ${data.query_type || '未知'}\n`;
                    alert(message);
                }
            })
            .catch(error => {
                console.error('获取错误SIM信息失败:', error);
                alert('获取SIM信息失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                errorBtn.innerHTML = originalText;
                errorBtn.disabled = false;
            });
    }

    // 初始化设备参数导出功能
    document.addEventListener('DOMContentLoaded', function() {
        const exportBtn = document.getElementById('exportParametersBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                exportDeviceParameters();
            });
        }
    });

    // 导出设备参数
    async function exportDeviceParameters() {
        try {
            const confirmResult = confirm('确定要导出设备参数吗？');
            if (!confirmResult) {
                return;
            }

            const exportBtn = document.getElementById('exportParametersBtn');
            const originalHTML = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
            exportBtn.disabled = true;

            const response = await fetch(`/debug_script/export_device_parameters/{{ device.id }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    format: 'xlsx'
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '导出失败');
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // 从响应头获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = `{{ device.device_id }}_parameters_${new Date().toISOString().slice(0,10)}.xlsx`;
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                }
            }

            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            alert('设备参数导出成功！');

        } catch (error) {
            console.error('导出设备参数失败:', error);
            alert('导出失败: ' + error.message);
        } finally {
            const exportBtn = document.getElementById('exportParametersBtn');
            exportBtn.innerHTML = '<i class="fas fa-download me-1"></i> 导出参数';
            exportBtn.disabled = false;
        }
    }

</script>
{% endblock %}