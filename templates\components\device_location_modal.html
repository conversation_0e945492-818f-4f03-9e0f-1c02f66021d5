<!-- 设备位置查询模态框 -->
<div class="modal fade" id="deviceLocationModal" tabindex="-1" aria-labelledby="deviceLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deviceLocationModalLabel">设备位置信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="deviceLocationLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备位置信息...</p>
                </div>
                <div id="deviceLocationContent" class="d-none">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">位置信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 30%;">位置代码:</th>
                                                <td id="location_code">--</td>
                                            </tr>
                                            <tr>
                                                <th>纬度:</th>
                                                <td id="location_latitude">--</td>
                                            </tr>
                                            <tr>
                                                <th>经度:</th>
                                                <td id="location_longitude">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">地图显示</h6>
                                </div>
                                <div class="card-body">
                                    <div id="location_map" style="height: 300px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">地图功能暂未实现</p>
                                                <small class="text-muted">可在此处集成地图API显示设备位置</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>位置代码</strong>: 设备的位置标识码</li>
                                    <li><strong>纬度/经度</strong>: 设备的GPS坐标信息</li>
                                    <li>位置信息用于设备的地理定位和管理</li>
                                    <li>坐标采用WGS84坐标系统</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="deviceLocationError" class="alert alert-danger d-none">
                    获取设备位置信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryDeviceLocationModal(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备位置信息（模态框版本）
function queryDeviceLocationModal(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('deviceLocationModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('deviceLocationLoading').classList.remove('d-none');
    document.getElementById('deviceLocationContent').classList.add('d-none');
    document.getElementById('deviceLocationError').classList.add('d-none');

    // 发送请求获取设备位置信息
    fetch(`/api/device/${window.deviceId}/location`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('deviceLocationLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('deviceLocationError').textContent = '获取设备位置信息失败: ' + data.error;
                document.getElementById('deviceLocationError').classList.remove('d-none');
                return;
            }

            if (data.success && data.location) {
                // 解析并显示位置信息
                displayDeviceLocation(data.location);
                // 显示内容区域
                document.getElementById('deviceLocationContent').classList.remove('d-none');
            } else {
                // 显示成功但无具体位置信息的情况
                document.getElementById('deviceLocationError').textContent = '设备位置查询成功，但未返回具体位置信息';
                document.getElementById('deviceLocationError').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('获取设备位置信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('deviceLocationLoading').classList.add('d-none');
            document.getElementById('deviceLocationError').textContent = '获取设备位置信息失败: ' + error.message;
            document.getElementById('deviceLocationError').classList.remove('d-none');
        });
}

// 显示设备位置信息
function displayDeviceLocation(data) {
    // 检查是否有位置信息
    if (!data) {
        document.getElementById('deviceLocationError').textContent = '设备返回的位置信息格式不正确';
        document.getElementById('deviceLocationError').classList.remove('d-none');
        return;
    }

    // 更新位置信息
    document.getElementById('location_code').textContent = data.location_code || '--';
    document.getElementById('location_latitude').textContent = data.latitude ? data.latitude.toFixed(7) + '°' : '--';
    document.getElementById('location_longitude').textContent = data.longitude ? data.longitude.toFixed(7) + '°' : '--';

    // 如果有有效的坐标，可以在这里添加地图显示逻辑
    if (data.latitude && data.longitude && data.latitude !== 0 && data.longitude !== 0) {
        // 更新地图显示区域
        const mapContainer = document.getElementById('location_map');
        mapContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt fa-3x text-success mb-3"></i>
                    <p class="text-success mb-1">位置: ${data.latitude.toFixed(7)}°, ${data.longitude.toFixed(7)}°</p>
                    <small class="text-muted">地图API集成后可显示具体位置</small>
                </div>
            </div>
        `;
    }
}
</script>
