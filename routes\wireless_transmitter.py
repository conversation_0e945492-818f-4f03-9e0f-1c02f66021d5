from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, send_file
from flask_login import login_required
from models.database import db
from models.wireless_transmitter import WirelessTransmitter
from sqlalchemy import or_, and_
from datetime import datetime
import logging
import pandas as pd
import io
from openpyxl import Workbook

# 创建蓝图
wireless_transmitter_bp = Blueprint('wireless_transmitter', __name__, url_prefix='/wireless_transmitter')

# 获取日志记录器
logger = logging.getLogger(__name__)

@wireless_transmitter_bp.route("/")
@login_required
def wireless_transmitter_list():
    """无线充电发射端管理页面"""
    try:
        # 获取发射端统计信息
        total_transmitters = WirelessTransmitter.query.count()
        
        # 按硬件版本统计
        bike_count = WirelessTransmitter.query.filter_by(hardware_version=1).count()
        tricycle_count = WirelessTransmitter.query.filter_by(hardware_version=2).count()
        
        stats = {
            "total": total_transmitters,
            "bike": bike_count,
            "tricycle": tricycle_count
        }
        
        return render_template(
            "wireless_transmitter.html",
            transmitters=[],  # 通过Ajax加载
            stats=stats
        )
    except Exception as e:
        logger.error(f"访问发射端管理页面失败: {e}")
        flash(f"访问发射端管理页面失败: {str(e)}", "error")
        return redirect(url_for('main.index'))

@wireless_transmitter_bp.route("/api/transmitters")
@login_required
def get_transmitters_api():
    """获取发射端列表API（支持分页、搜索、筛选）"""
    try:
        # 获取分页参数
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)
        
        # 获取搜索和筛选参数
        search = request.args.get("search", "").strip()
        hardware_filter = request.args.get("hardware", "all")
        owning_entity_filter = request.args.get("owning_entity", "").strip()
        binding_filter = request.args.get("binding", "all")
        start_date = request.args.get("start_date", "").strip()
        end_date = request.args.get("end_date", "").strip()
        
        # 构建查询
        query = WirelessTransmitter.query
        
        # 搜索条件
        if search:
            search_conditions = []
            # 尝试按发射端ID搜索（精确匹配或模糊匹配）
            try:
                search_id = int(search)
                search_conditions.append(WirelessTransmitter.transmitter_id == search_id)
            except ValueError:
                pass
            
            # 按备注搜索
            search_conditions.append(WirelessTransmitter.transmitter_remark.ilike(f"%{search}%"))
            
            # 按主题搜索
            search_conditions.append(WirelessTransmitter.owning_entity.ilike(f"%{search}%"))
            
            # 按中控ID搜索
            try:
                controller_id = int(search)
                search_conditions.append(WirelessTransmitter.controller_id == controller_id)
            except ValueError:
                pass
            
            if search_conditions:
                query = query.filter(or_(*search_conditions))
        
        # 硬件版本筛选
        if hardware_filter != "all":
            try:
                hardware_version = int(hardware_filter)
                query = query.filter(WirelessTransmitter.hardware_version == hardware_version)
            except ValueError:
                pass
        
        # 主题筛选
        if owning_entity_filter:
            query = query.filter(WirelessTransmitter.owning_entity.ilike(f"%{owning_entity_filter}%"))

        # 绑定状态筛选
        if binding_filter == "bound":
            query = query.filter(WirelessTransmitter.controller_id.isnot(None))
        elif binding_filter == "unbound":
            query = query.filter(WirelessTransmitter.controller_id.is_(None))

        # 日期范围筛选
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                query = query.filter(WirelessTransmitter.register_date >= start_datetime)
            except ValueError:
                pass

        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
                # 包含整个结束日期
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(WirelessTransmitter.register_date <= end_datetime)
            except ValueError:
                pass
        
        # 排序
        query = query.order_by(WirelessTransmitter.register_date.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        transmitters = pagination.items
        
        # 转换为字典格式
        transmitters_data = []
        for transmitter in transmitters:
            transmitter_dict = transmitter.to_dict()
            transmitters_data.append(transmitter_dict)
        
        return jsonify({
            "success": True,
            "transmitters": transmitters_data,
            "pagination": {
                "page": pagination.page,
                "pages": pagination.pages,
                "per_page": pagination.per_page,
                "total": pagination.total,
                "has_prev": pagination.has_prev,
                "has_next": pagination.has_next
            }
        })
        
    except Exception as e:
        logger.error(f"获取发射端列表失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取发射端列表失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters", methods=["POST"])
@login_required
def add_transmitter_api():
    """添加发射端API"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        if not data.get("transmitter_id"):
            return jsonify({
                "success": False,
                "message": "发射端ID不能为空"
            }), 400
        
        transmitter_id = int(data["transmitter_id"])
        
        # 检查发射端ID是否已存在
        existing = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if existing:
            return jsonify({
                "success": False,
                "message": f"发射端ID {transmitter_id} 已存在"
            }), 400
        
        # 处理软件版本，默认为0.0.0
        software_version_string = data.get("software_version_string", "0.0.0").strip()
        if not software_version_string:
            software_version_string = "0.0.0"
        software_version = WirelessTransmitter.version_string_to_int(software_version_string)
        
        # 创建新发射端
        transmitter = WirelessTransmitter(
            transmitter_id=transmitter_id,
            transmitter_remark=data.get("transmitter_remark", ""),
            hardware_version=int(data.get("hardware_version", 1)),
            software_version=software_version,
            controller_id=int(data["controller_id"]) if data.get("controller_id") else None,
            owning_entity=data.get("owning_entity", "")
        )
        
        db.session.add(transmitter)
        db.session.commit()
        
        logger.info(f"成功添加发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端添加成功",
            "transmitter": transmitter.to_dict()
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "message": f"数据格式错误: {str(e)}"
        }), 400
    except Exception as e:
        db.session.rollback()
        logger.error(f"添加发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"添加发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>", methods=["PUT"])
@login_required
def update_transmitter_api(transmitter_id):
    """更新发射端API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        data = request.get_json()
        
        # 更新字段
        if "transmitter_remark" in data:
            transmitter.transmitter_remark = data["transmitter_remark"]
        
        if "hardware_version" in data:
            transmitter.hardware_version = int(data["hardware_version"])
        
        if "software_version_string" in data:
            transmitter.software_version = WirelessTransmitter.version_string_to_int(data["software_version_string"])
        
        if "controller_id" in data:
            transmitter.controller_id = int(data["controller_id"]) if data["controller_id"] else None
        
        if "owning_entity" in data:
            transmitter.owning_entity = data["owning_entity"]
        
        transmitter.updated_at = datetime.now()
        
        db.session.commit()
        
        logger.info(f"成功更新发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端更新成功",
            "transmitter": transmitter.to_dict()
        })
        
    except ValueError as e:
        return jsonify({
            "success": False,
            "message": f"数据格式错误: {str(e)}"
        }), 400
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"更新发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>", methods=["DELETE"])
@login_required
def delete_transmitter_api(transmitter_id):
    """删除发射端API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        db.session.delete(transmitter)
        db.session.commit()
        
        logger.info(f"成功删除发射端: {transmitter_id}")
        
        return jsonify({
            "success": True,
            "message": "发射端删除成功"
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除发射端失败: {e}")
        return jsonify({
            "success": False,
            "message": f"删除发射端失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/transmitters/<int:transmitter_id>")
@login_required
def get_transmitter_api(transmitter_id):
    """获取单个发射端信息API"""
    try:
        transmitter = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
        if not transmitter:
            return jsonify({
                "success": False,
                "message": "发射端不存在"
            }), 404
        
        return jsonify({
            "success": True,
            "transmitter": transmitter.to_dict()
        })

    except Exception as e:
        logger.error(f"获取发射端信息失败: {e}")
        return jsonify({
            "success": False,
            "message": f"获取发射端信息失败: {str(e)}"
        }), 500

@wireless_transmitter_bp.route("/api/batch_import", methods=["POST"])
@login_required
def batch_import_transmitters():
    """批量导入发射端"""
    if "import_file" not in request.files:
        return jsonify({"error": "没有上传文件"}), 400

    file = request.files["import_file"]
    if file.filename == "":
        return jsonify({"error": "没有选择文件"}), 400

    if not file.filename.endswith((".xlsx", ".xls")):
        return jsonify({"error": "请上传Excel文件"}), 400

    try:
        # 读取Excel文件
        df = pd.read_excel(file)

        # 验证必要的列是否存在
        required_columns = ["发射端ID"]
        if not all(col in df.columns for col in required_columns):
            return jsonify({"error": "Excel文件格式不正确，请使用正确的模板"}), 400

        results = []
        success_count = 0
        existing_count = 0
        failed_count = 0

        for index, row in df.iterrows():
            transmitter_id = row.get("发射端ID")
            if pd.isna(transmitter_id):
                continue

            try:
                transmitter_id = int(transmitter_id)
            except (ValueError, TypeError):
                results.append({"transmitter_id": str(transmitter_id), "status": "failed", "message": "发射端ID格式错误"})
                failed_count += 1
                continue

            # 检查是否已存在
            existing = WirelessTransmitter.query.filter_by(transmitter_id=transmitter_id).first()
            if existing:
                results.append({"transmitter_id": transmitter_id, "status": "existing", "message": "发射端ID已存在"})
                existing_count += 1
                continue

            try:
                # 获取其他字段
                transmitter_remark = row.get("发射端备注", "")
                if pd.isna(transmitter_remark):
                    transmitter_remark = ""

                hardware_version = row.get("硬件版本", 1)
                if pd.isna(hardware_version):
                    hardware_version = 1
                else:
                    try:
                        hardware_version = int(hardware_version)
                        if hardware_version not in [1, 2]:
                            hardware_version = 1
                    except (ValueError, TypeError):
                        hardware_version = 1

                software_version_string = row.get("软件版本", "0.0.0")
                if pd.isna(software_version_string) or software_version_string == "":
                    software_version_string = "0.0.0"

                software_version = WirelessTransmitter.version_string_to_int(str(software_version_string))

                controller_id = row.get("绑定中控ID")
                if not pd.isna(controller_id):
                    try:
                        controller_id = int(controller_id)
                    except (ValueError, TypeError):
                        controller_id = None
                else:
                    controller_id = None

                owning_entity = row.get("所属主体", "")
                if pd.isna(owning_entity):
                    owning_entity = ""

                # 创建新发射端
                new_transmitter = WirelessTransmitter(
                    transmitter_id=transmitter_id,
                    transmitter_remark=str(transmitter_remark),
                    hardware_version=hardware_version,
                    software_version=software_version,
                    controller_id=controller_id,
                    owning_entity=str(owning_entity)
                )
                db.session.add(new_transmitter)
                db.session.commit()

                results.append({"transmitter_id": transmitter_id, "status": "success", "message": "导入成功"})
                success_count += 1

            except Exception as e:
                db.session.rollback()
                results.append({"transmitter_id": transmitter_id, "status": "failed", "message": f"导入失败：{str(e)}"})
                failed_count += 1

        return jsonify({
            "success_count": success_count,
            "existing_count": existing_count,
            "failed_count": failed_count,
            "results": results,
        })

    except Exception as e:
        return jsonify({"error": f"处理文件时发生错误：{str(e)}"}), 500

@wireless_transmitter_bp.route("/api/download_template")
@login_required
def download_template():
    """下载导入模板"""
    try:
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "发射端导入模板"

        # 设置表头
        headers = [
            "发射端ID",
            "发射端备注",
            "硬件版本",
            "软件版本",
            "绑定中控ID",
            "所属主体"
        ]

        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # 添加示例数据
        example_data = [
            [1001, "测试发射端1", 1, "1.0.0", 2001, "test/owning_entity1"],
            [1002, "测试发射端2", 2, "1.1.0", 2002, "test/owning_entity2"],
        ]

        for row_idx, row_data in enumerate(example_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 添加说明
        ws.cell(row=5, column=1, value="说明：")
        ws.cell(row=6, column=1, value="1. 发射端ID为必填项，必须唯一")
        ws.cell(row=7, column=1, value="2. 硬件版本：1=电动自行车，2=电动三轮车")
        ws.cell(row=8, column=1, value="3. 软件版本格式：major.minor.patch (如1.0.0)")
        ws.cell(row=9, column=1, value="4. 其他字段为选填项")

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='无线充电发射端导入模板.xlsx'
        )

    except Exception as e:
        logger.error(f"生成模板失败: {e}")
        return jsonify({"error": f"生成模板失败: {str(e)}"}), 500
