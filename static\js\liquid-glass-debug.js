/**
 * 液态玻璃主题调试工具
 * 用于诊断和修复样式问题
 */

class LiquidGlassDebugger {
    constructor() {
        this.isDebugMode = false;
        this.styleObserver = null;
        this.init();
    }

    init() {
        // 添加调试控制台命令
        window.liquidGlassDebug = this;
        
        // 监听主题变化
        document.addEventListener('themeChanged', (e) => {
            if (e.detail.theme === 'liquidGlass') {
                this.startDebugging();
            } else {
                this.stopDebugging();
            }
        });

        // 如果当前已经是液态玻璃主题，开始调试
        if (document.body.classList.contains('liquid-glass-theme')) {
            this.startDebugging();
        }
    }

    /**
     * 开始调试模式
     */
    startDebugging() {
        this.isDebugMode = true;
        console.log('🔍 液态玻璃主题调试模式已启动');
        
        // 监听样式变化
        this.observeStyleChanges();
        
        // 检查初始样式状态
        this.checkInitialStyles();
        
        // 定期检查样式
        this.startPeriodicCheck();
    }

    /**
     * 停止调试模式
     */
    stopDebugging() {
        this.isDebugMode = false;
        console.log('🔍 液态玻璃主题调试模式已停止');
        
        if (this.styleObserver) {
            this.styleObserver.disconnect();
            this.styleObserver = null;
        }

        if (this.periodicCheckInterval) {
            clearInterval(this.periodicCheckInterval);
            this.periodicCheckInterval = null;
        }
    }

    /**
     * 监听样式变化
     */
    observeStyleChanges() {
        if (this.styleObserver) {
            this.styleObserver.disconnect();
        }

        this.styleObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    this.onClassChange(mutation.target);
                } else if (mutation.type === 'childList') {
                    // 检查是否有新的样式表被添加
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.tagName === 'LINK' && node.rel === 'stylesheet') {
                                this.onStylesheetAdded(node);
                            } else if (node.tagName === 'STYLE') {
                                this.onInlineStyleAdded(node);
                            }
                        }
                    });
                }
            });
        });

        // 监听body和head的变化
        this.styleObserver.observe(document.body, {
            attributes: true,
            attributeFilter: ['class', 'style']
        });

        this.styleObserver.observe(document.head, {
            childList: true,
            subtree: true
        });
    }

    /**
     * 类名变化处理
     */
    onClassChange(target) {
        if (target === document.body) {
            const hasLiquidGlass = target.classList.contains('liquid-glass-theme');
            console.log(`🎨 Body类名变化: liquid-glass-theme = ${hasLiquidGlass}`);
            
            if (hasLiquidGlass && this.isDebugMode) {
                // 延迟检查样式，确保CSS已应用
                setTimeout(() => {
                    this.checkStyleApplication();
                }, 100);
            }
        }
    }

    /**
     * 样式表添加处理
     */
    onStylesheetAdded(linkElement) {
        console.log(`📄 新样式表添加: ${linkElement.href}`);
        
        // 如果是液态玻璃相关的样式表，检查加载状态
        if (linkElement.href && linkElement.href.includes('liquid-glass')) {
            linkElement.addEventListener('load', () => {
                console.log(`✅ 液态玻璃样式表加载完成: ${linkElement.href}`);
                this.checkStyleApplication();
            });

            linkElement.addEventListener('error', () => {
                console.error(`❌ 液态玻璃样式表加载失败: ${linkElement.href}`);
            });
        }
    }

    /**
     * 内联样式添加处理
     */
    onInlineStyleAdded(styleElement) {
        if (styleElement.textContent.includes('liquid-glass') || 
            styleElement.textContent.includes('glass-effect')) {
            console.log('📝 液态玻璃内联样式添加');
            this.checkStyleApplication();
        }
    }

    /**
     * 检查初始样式状态
     */
    checkInitialStyles() {
        console.log('🔍 检查初始样式状态...');
        
        const report = this.generateStyleReport();
        console.table(report.summary);
        
        if (report.issues.length > 0) {
            console.warn('⚠️ 发现样式问题:');
            report.issues.forEach(issue => console.warn(`  - ${issue}`));
        } else {
            console.log('✅ 样式状态正常');
        }
    }

    /**
     * 检查样式应用情况
     */
    checkStyleApplication() {
        if (!this.isDebugMode) return;

        const report = this.generateStyleReport();
        
        // 检查是否有新问题
        const newIssues = report.issues.filter(issue => 
            !this.lastKnownIssues || !this.lastKnownIssues.includes(issue)
        );

        if (newIssues.length > 0) {
            console.warn('🚨 检测到新的样式问题:');
            newIssues.forEach(issue => console.warn(`  - ${issue}`));
            
            // 尝试自动修复
            this.attemptAutoFix();
        }

        this.lastKnownIssues = report.issues;
    }

    /**
     * 生成样式报告
     */
    generateStyleReport() {
        const body = document.body;
        const isLiquidGlass = body.classList.contains('liquid-glass-theme');
        const issues = [];
        
        // 检查基本状态
        if (!isLiquidGlass) {
            issues.push('Body缺少liquid-glass-theme类');
        }

        // 检查CSS变量
        const styles = getComputedStyle(document.documentElement);
        const cssVars = {
            '--glass-blur': styles.getPropertyValue('--glass-blur').trim(),
            '--glass-bg': styles.getPropertyValue('--glass-bg').trim(),
            '--glass-border': styles.getPropertyValue('--glass-border').trim(),
            '--glass-shadow': styles.getPropertyValue('--glass-shadow').trim()
        };

        Object.entries(cssVars).forEach(([varName, value]) => {
            if (!value) {
                issues.push(`CSS变量${varName}未定义`);
            }
        });

        // 检查背景样式
        const bodyStyles = getComputedStyle(body);
        const background = bodyStyles.background;
        if (isLiquidGlass && !background.includes('linear-gradient')) {
            issues.push('Body背景渐变未应用');
        }

        // 检查卡片样式
        const card = document.querySelector('.card');
        if (card && isLiquidGlass) {
            const cardStyles = getComputedStyle(card);
            const backdropFilter = cardStyles.backdropFilter || cardStyles.webkitBackdropFilter;
            if (!backdropFilter || backdropFilter === 'none') {
                issues.push('卡片毛玻璃效果未应用');
            }
        }

        // 检查CSS文件加载状态
        const liquidGlassLinks = document.querySelectorAll('link[href*="liquid-glass"]');
        if (liquidGlassLinks.length === 0) {
            issues.push('液态玻璃CSS文件未加载');
        }

        return {
            summary: {
                '主题状态': isLiquidGlass ? '液态玻璃' : '默认',
                'CSS变量数量': Object.values(cssVars).filter(v => v).length,
                '背景渐变': background.includes('linear-gradient') ? '已应用' : '未应用',
                'CSS文件数量': liquidGlassLinks.length,
                '问题数量': issues.length
            },
            issues,
            cssVars,
            liquidGlassLinks: Array.from(liquidGlassLinks).map(link => link.href)
        };
    }

    /**
     * 尝试自动修复
     */
    attemptAutoFix() {
        console.log('🔧 尝试自动修复样式问题...');
        
        const body = document.body;
        
        // 确保主题类存在
        if (!body.classList.contains('liquid-glass-theme')) {
            body.classList.add('liquid-glass-theme');
            console.log('✅ 已添加liquid-glass-theme类');
        }

        // 重新加载主题切换器
        if (window.themeSwitcher) {
            window.themeSwitcher.ensureThemeApplied();
            console.log('✅ 已重新应用主题');
        }

        // 强制重新计算样式
        body.offsetHeight;
        
        console.log('🔧 自动修复完成');
    }

    /**
     * 开始定期检查
     */
    startPeriodicCheck() {
        if (this.periodicCheckInterval) {
            clearInterval(this.periodicCheckInterval);
        }

        this.periodicCheckInterval = setInterval(() => {
            if (this.isDebugMode) {
                this.checkStyleApplication();
            }
        }, 5000); // 每5秒检查一次
    }

    /**
     * 手动触发样式检查
     */
    checkNow() {
        const report = this.generateStyleReport();
        console.log('📊 当前样式状态:');
        console.table(report.summary);
        
        if (report.issues.length > 0) {
            console.warn('⚠️ 发现问题:');
            report.issues.forEach(issue => console.warn(`  - ${issue}`));
        } else {
            console.log('✅ 样式状态正常');
        }
        
        return report;
    }

    /**
     * 强制修复样式
     */
    forceFix() {
        console.log('🔧 强制修复样式...');
        this.attemptAutoFix();
        
        setTimeout(() => {
            const report = this.checkNow();
            if (report.issues.length === 0) {
                console.log('✅ 修复成功');
            } else {
                console.warn('⚠️ 仍有问题需要手动处理');
            }
        }, 1000);
    }
}

// 初始化调试器
document.addEventListener('DOMContentLoaded', () => {
    window.liquidGlassDebugger = new LiquidGlassDebugger();
    
    // 添加控制台帮助信息
    console.log(`
🎨 液态玻璃主题调试工具已加载
可用命令:
- liquidGlassDebug.checkNow() - 检查当前样式状态
- liquidGlassDebug.forceFix() - 强制修复样式问题
- liquidGlassDebug.startDebugging() - 开始调试模式
- liquidGlassDebug.stopDebugging() - 停止调试模式
    `);
});
