<!-- 无线充电映射表查询模态框 -->
<div class="modal fade" id="wirelessChargerModal" tabindex="-1" aria-labelledby="wirelessChargerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wirelessChargerModalLabel">无线充电映射表信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="wirelessChargerLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取无线充电映射表信息...</p>
                </div>
                <div id="wirelessChargerContent" class="d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">基本信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 50%;">基准插座ID:</th>
                                                <td id="base_plug_id">--</td>
                                            </tr>
                                            <tr>
                                                <th>最大插座数量:</th>
                                                <td id="max_plug_num">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">映射状态</h6>
                                </div>
                                <div class="card-body">
                                    <div id="mapping_status">
                                        <!-- 映射状态将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">插座到发射端映射表</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>插座索引</th>
                                                    <th>插座ID</th>
                                                    <th>发射端ID</th>
                                                    <th>连接状态</th>
                                                </tr>
                                            </thead>
                                            <tbody id="mappingTableBody">
                                                <!-- 映射表数据将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>基准插座ID</strong>: 无线充电插座的起始ID编号</li>
                                    <li><strong>插座到发射端映射</strong>: 显示每个插座对应的发射端设备ID</li>
                                    <li><strong>连接状态</strong>: 显示插座是否已连接到发射端设备</li>
                                    <li>映射表用于管理无线充电设备的配对关系</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="wirelessChargerError" class="alert alert-danger d-none">
                    获取无线充电映射表信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryWirelessChargerInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询无线充电映射表信息
function queryWirelessChargerInfo(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('wirelessChargerModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('wirelessChargerLoading').classList.remove('d-none');
    document.getElementById('wirelessChargerContent').classList.add('d-none');
    document.getElementById('wirelessChargerError').classList.add('d-none');

    // 发送请求获取无线充电映射表信息
    fetch(`/api/device/${window.deviceId}/wireless_charger_info`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('wirelessChargerLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('wirelessChargerError').textContent = '获取无线充电映射表信息失败: ' + data.error;
                document.getElementById('wirelessChargerError').classList.remove('d-none');
                return;
            }

            // 解析并显示无线充电映射表信息
            displayWirelessChargerInfo(data);

            // 显示内容区域
            document.getElementById('wirelessChargerContent').classList.remove('d-none');
        })
        .catch(error => {
            console.error('获取无线充电映射表信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('wirelessChargerLoading').classList.add('d-none');
            document.getElementById('wirelessChargerError').textContent = '获取无线充电映射表信息失败: ' + error.message;
            document.getElementById('wirelessChargerError').classList.remove('d-none');
        });
}

// 显示无线充电映射表信息
function displayWirelessChargerInfo(data) {
    // 检查是否有无线充电映射表信息
    if (!data || !data.info) {
        document.getElementById('wirelessChargerError').textContent = '设备返回的无线充电映射表信息格式不正确';
        document.getElementById('wirelessChargerError').classList.remove('d-none');
        return;
    }

    const info = data.info;

    // 更新基本信息
    document.getElementById('base_plug_id').textContent = info.base_plug_id || '--';
    document.getElementById('max_plug_num').textContent = info.max_plug_num || '--';

    // 更新映射状态
    const mappingStatus = document.getElementById('mapping_status');
    const connectedCount = info.txer_id_map ? info.txer_id_map.filter(id => id !== 0xFFFFFFFF).length : 0;
    const totalCount = info.max_plug_num || 0;
    
    mappingStatus.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>已连接插座:</span>
            <span class="badge bg-success">${connectedCount}</span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>未连接插座:</span>
            <span class="badge bg-secondary">${totalCount - connectedCount}</span>
        </div>
        <div class="d-flex justify-content-between align-items-center">
            <span>总插座数:</span>
            <span class="badge bg-primary">${totalCount}</span>
        </div>
    `;

    // 填充映射表
    const tableBody = document.getElementById('mappingTableBody');
    tableBody.innerHTML = '';

    if (info.txer_id_map && info.max_plug_num) {
        for (let i = 0; i < info.max_plug_num; i++) {
            const row = document.createElement('tr');
            
            // 插座索引
            const indexCell = document.createElement('td');
            indexCell.innerHTML = `<strong>${i}</strong>`;
            row.appendChild(indexCell);
            
            // 插座ID
            const plugIdCell = document.createElement('td');
            const plugId = (info.base_plug_id || 0) + i;
            plugIdCell.textContent = plugId;
            row.appendChild(plugIdCell);
            
            // 发射端ID
            const txerIdCell = document.createElement('td');
            const txerId = info.txer_id_map[i];
            if (txerId === 0xFFFFFFFF) {
                txerIdCell.innerHTML = '<span class="text-muted">未连接</span>';
            } else {
                txerIdCell.innerHTML = `<code>0x${txerId.toString(16).toUpperCase().padStart(8, '0')}</code>`;
            }
            row.appendChild(txerIdCell);
            
            // 连接状态
            const statusCell = document.createElement('td');
            if (txerId === 0xFFFFFFFF) {
                statusCell.innerHTML = '<span class="badge bg-secondary">未连接</span>';
                row.classList.add('table-light');
            } else {
                statusCell.innerHTML = '<span class="badge bg-success">已连接</span>';
            }
            row.appendChild(statusCell);
            
            tableBody.appendChild(row);
        }
    }
}
</script>
