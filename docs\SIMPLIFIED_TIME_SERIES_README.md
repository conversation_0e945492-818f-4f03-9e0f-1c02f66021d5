# 简化时序数据模型重构说明

## 概述

根据您的需求，我们对时序数据存储结构进行了重大简化，现在只存储原始硬件数据，在代码层面进行解析，大大减少了存储空间占用并提高了性能。

## 主要变化

### 1. 数据模型简化

**之前的复杂结构:**
- 使用JSON字段存储解析后的数据
- 每个数据类型单独存储一条记录
- 需要TimeSeriesDataBatch表进行批次管理
- 存储大量冗余的字段名称和元数据

**现在的简化结构:**
- 只存储原始硬件数据
- 一条记录包含所有传感器数据
- 去除了batch表
- 在代码层面进行数据解析

### 2. 新的数据字段

基于您提供的`HandleHardwareInfoQuery`函数，新的表结构包含以下原始数据字段：

```sql
-- 基础字段
device_id VARCHAR(50)           -- 设备ID
timestamp TIMESTAMP             -- 时间戳

-- 原始硬件数据字段
bl0910_error_count INTEGER      -- BL0910错误计数
bl0910_rms_values INTEGER[10]   -- 10个通道的BL0910 RMS寄存器值
relay_state SMALLINT            -- 继电器状态 (16位)
short_period_error_count SMALLINT -- 短周期错误计数
long_period_error_count SMALLINT  -- 长周期错误计数
last_zero_cross_time INTEGER    -- 最后零交叉时间
voltage_raw SMALLINT            -- 电压原始值 (实际值 * 100)
temperature_raw SMALLINT        -- 温度原始值 (实际值 * 100)
total_power_raw INTEGER         -- 总功率原始值 (实际值 * 100)
csq SMALLINT                    -- 信号质量
ber SMALLINT                    -- 误码率
relay_pull_fault SMALLINT       -- 继电器拉合故障
relay_open_fault SMALLINT       -- 继电器分断故障
```

### 3. 代码层面的数据解析

新模型提供了丰富的解析方法：

```python
# 获取解析后的数值
voltage = record.get_voltage()          # 电压值（伏特）
temperature = record.get_temperature()  # 温度值（摄氏度）
total_power = record.get_total_power()  # 总功率值（瓦特）

# 继电器状态解析
relay_states = record.get_relay_states()  # 返回字典 {'relay_1': True, ...}

# 通道功率解析
channel_powers = record.get_all_channel_powers()  # 返回10个通道的功率列表
power_ch1 = record.get_channel_power(1)          # 获取指定通道功率

# 错误检查
has_errors = record.has_errors()  # 检查是否有任何错误
```

## 存储空间对比

### 之前的存储方式
```
每个设备每次数据采集需要存储约15-20条记录：
- 10条功率通道记录
- 1条功率汇总记录  
- 8条单值数据记录（电压、温度、CSQ等）
- 1条batch记录

每条记录包含：
- JSON数据字段（平均100-200字节）
- 字符串字段名称（20-50字节）
- 元数据字段（50字节）
总计：每次采集约3-5KB
```

### 现在的存储方式
```
每个设备每次数据采集只需要1条记录：
- 所有原始数据存储在一条记录中
- 只存储数值，不存储字段名称
- 使用PostgreSQL数组类型存储多通道数据

每条记录大小：
- 原始数值字段（约80字节）
- 数组字段（40字节）
- 基础字段（30字节）
总计：每次采集约150字节
```

**存储空间节省：约95%**

## 性能优化

### 1. 索引策略优化
```sql
-- 主要查询索引
CREATE INDEX idx_device_timestamp ON time_series_data(device_id, timestamp);
CREATE INDEX idx_timestamp_device ON time_series_data(timestamp, device_id);

-- 按时间分区的索引
CREATE INDEX idx_date_device ON time_series_data(date_trunc('day', timestamp), device_id);
CREATE INDEX idx_hour_device ON time_series_data(date_trunc('hour', timestamp), device_id);

-- 条件索引（只对有值的记录建索引）
CREATE INDEX idx_voltage_range ON time_series_data(voltage_raw) WHERE voltage_raw IS NOT NULL;
CREATE INDEX idx_temperature_range ON time_series_data(temperature_raw) WHERE temperature_raw IS NOT NULL;

-- 错误监控索引
CREATE INDEX idx_bl0910_error ON time_series_data(bl0910_error_count) WHERE bl0910_error_count > 0;
```

### 2. 查询性能提升
- 单表查询，避免了复杂的JOIN操作
- 减少了数据扫描量
- 优化的索引策略提高查询速度

## 使用方法

### 1. 写入数据

**方式1：使用解析后的数据**
```python
time_series_service.write_sensor_data(
    device_id="charging_pile_001",
    bl0910_error_count=0,
    bl0910_rms_values=[1500, 1520, 1480, ...],  # 10个通道
    relay_state=0b1111111111000000,
    voltage=220.5,
    temperature=35.8,
    total_power=1500.25,
    csq=25,
    ber=2
)
```

**方式2：直接从硬件数据包写入**
```python
# hardware_data 是从HandleHardwareInfoQuery得到的原始字节数据
time_series_service.write_hardware_info_data(device_id, hardware_data)
```

### 2. 查询数据

```python
# 查询电压数据
voltage_data = time_series_service.query_voltage_data(device_id, start_time, end_time)

# 查询温度数据
temperature_data = time_series_service.query_temperature_data(device_id, start_time, end_time)

# 查询功率数据（包含所有通道）
power_data = time_series_service.query_power_data(device_id, start_time, end_time)

# 查询信号质量数据
csq_data = time_series_service.query_csq_data(device_id, start_time, end_time)
```

## 迁移步骤

### 1. 运行迁移脚本
```bash
python sql_tools/migrate_to_simplified_time_series.py
```

### 2. 运行测试
```bash
python tests/test_simplified_time_series.py
```

### 3. 查看使用示例
```bash
python examples/simplified_time_series_usage.py
```

## 继电器状态解析示例

基于您提供的`BFL_Relay_GetAllRelayState`函数：

```python
# 继电器状态是16位整数，每一位代表一个继电器
relay_state = 0b1010101010101010

# 解析继电器状态
relay_states = record.get_relay_states()
# 返回: {'relay_1': False, 'relay_2': True, 'relay_3': False, ...}

# 检查特定继电器状态
if relay_states['relay_1']:
    print("继电器1已激活")
```

## 功率计算

虽然我们存储的是BL0910 RMS寄存器的原始值，但您可以在`get_channel_power`方法中实现实际的功率计算公式：

```python
def get_channel_power(self, channel: int) -> float:
    """根据BL0910 RMS寄存器值计算指定通道的功率"""
    if (self.bl0910_rms_values is None or 
        channel < 1 or channel > 10 or 
        len(self.bl0910_rms_values) < channel):
        return None
    
    rms_value = self.bl0910_rms_values[channel - 1]
    
    # TODO: 在这里实现您的实际功率计算公式
    # 例如：power = (rms_value * voltage * current_factor) / 1000
    # 目前返回原始值作为占位符
    return float(rms_value) if rms_value is not None else None
```

## 优势总结

1. **存储空间节省95%** - 只存储原始数据，不存储字段名称和解析结果
2. **查询性能提升** - 单表查询，优化的索引策略
3. **代码灵活性** - 解析逻辑在代码中，易于修改和扩展
4. **数据完整性** - 存储所有原始数据，不丢失任何信息
5. **维护简单** - 去除了复杂的batch表和JSON字段
6. **硬件兼容** - 直接支持从硬件数据包写入

这个重构完全符合您"只存储原始适当解析的数据，读取时在代码层面对复杂的比如继电器状态之类的解析"的需求。
