<!-- 电流RMS信息查询模态框 -->
<div class="modal fade" id="currentRmsModal" tabindex="-1" aria-labelledby="currentRmsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currentRmsModalLabel">设备电流RMS信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="currentRmsLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备电流RMS信息...</p>
                </div>
                <div id="currentRmsContent" class="d-none">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">电流RMS寄存器值</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>通道</th>
                                                    <th>电流RMS寄存器值</th>
                                                    <th>功率最大值 (W)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="currentRmsTableBody">
                                                <!-- 电流RMS数据将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>电流RMS寄存器值</strong>: BL0910芯片各通道的电流有效值寄存器原始数据</li>
                                    <li><strong>功率最大值</strong>: 各通道记录的功率峰值，单位为瓦(W)</li>
                                    <li>这些数据用于设备的电流监测和功率分析</li>
                                    <li>RMS值反映了交流电流的有效值大小</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="currentRmsError" class="alert alert-danger d-none">
                    获取设备电流RMS信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryCurrentRmsInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备电流RMS信息
function queryCurrentRmsInfo(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('currentRmsModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('currentRmsLoading').classList.remove('d-none');
    document.getElementById('currentRmsContent').classList.add('d-none');
    document.getElementById('currentRmsError').classList.add('d-none');

    // 发送请求获取设备电流RMS信息
    fetch(`/api/device/${window.deviceId}/current_rms_info`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('currentRmsLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('currentRmsError').textContent = '获取设备电流RMS信息失败: ' + data.error;
                document.getElementById('currentRmsError').classList.remove('d-none');
                return;
            }

            // 解析并显示电流RMS信息
            displayCurrentRmsInfo(data);

            // 显示内容区域
            document.getElementById('currentRmsContent').classList.remove('d-none');
        })
        .catch(error => {
            console.error('获取设备电流RMS信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('currentRmsLoading').classList.add('d-none');
            document.getElementById('currentRmsError').textContent = '获取设备电流RMS信息失败: ' + error.message;
            document.getElementById('currentRmsError').classList.remove('d-none');
        });
}

// 显示电流RMS信息
function displayCurrentRmsInfo(data) {
    // 检查是否有电流RMS信息
    if (!data || !data.info) {
        document.getElementById('currentRmsError').textContent = '设备返回的电流RMS信息格式不正确';
        document.getElementById('currentRmsError').classList.remove('d-none');
        return;
    }

    const info = data.info;
    const tableBody = document.getElementById('currentRmsTableBody');
    tableBody.innerHTML = '';

    // 通道名称映射
    const channelNames = [
        'MEASURE_1', 'MEASURE_2', 'MEASURE_3', 'MEASURE_4', 'MEASURE_5',
        'MEASURE_6', 'MEASURE_7', 'MEASURE_8', 'MEASURE_9', 'MEASURE_10'
    ];

    // 填充表格数据
    for (let i = 0; i < 10; i++) {
        const row = document.createElement('tr');
        
        // 通道名称
        const channelCell = document.createElement('td');
        channelCell.innerHTML = `<strong>${channelNames[i]}</strong>`;
        row.appendChild(channelCell);
        
        // 电流RMS寄存器值
        const rmsCell = document.createElement('td');
        const rmsValue = info.current_rms_regs ? info.current_rms_regs[i] : 0;
        rmsCell.textContent = rmsValue ? `0x${rmsValue.toString(16).toUpperCase().padStart(8, '0')} (${rmsValue})` : '--';
        row.appendChild(rmsCell);
        
        // 功率最大值
        const powerCell = document.createElement('td');
        const powerValue = info.max_power_values ? info.max_power_values[i]*0.001 : 0;
        powerCell.innerHTML = powerValue ? `<span class="badge bg-success">${powerValue.toFixed(3)} W</span>` : '--';
        row.appendChild(powerCell);
        
        tableBody.appendChild(row);
    }
}
</script>
