<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新API测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>新增API功能测试</h1>
        <div class="row">
            <div class="col-12">
                <h3>设备75的新增查询功能</h3>
                <button class="btn btn-primary me-2" onclick="testCurrentRmsApi()">
                    <i class="fas fa-chart-line me-1"></i> 测试电流RMS API
                </button>
                <button class="btn btn-secondary me-2" onclick="testWirelessChargerApi()">
                    <i class="fas fa-wifi me-1"></i> 测试无线充电映射API
                </button>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-12">
                <div id="results" class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="resultContent">点击上方按钮开始测试...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testCurrentRmsApi() {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">测试中...</span></div> 正在测试电流RMS API...';
            
            fetch('/api/device/75/current_rms_info')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    resultDiv.innerHTML = `
                        <h6 class="text-success">电流RMS API测试成功</h6>
                        <pre class="bg-light p-3">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <h6 class="text-danger">电流RMS API测试失败</h6>
                        <div class="alert alert-danger">${error.message}</div>
                    `;
                });
        }

        function testWirelessChargerApi() {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">测试中...</span></div> 正在测试无线充电映射API...';
            
            fetch('/api/device/75/wireless_charger_info')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    resultDiv.innerHTML = `
                        <h6 class="text-success">无线充电映射API测试成功</h6>
                        <pre class="bg-light p-3">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <h6 class="text-danger">无线充电映射API测试失败</h6>
                        <div class="alert alert-danger">${error.message}</div>
                    `;
                });
        }
    </script>
</body>
</html>
