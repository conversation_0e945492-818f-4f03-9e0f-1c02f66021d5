#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化时序数据模型测试脚本
测试新的原始硬件数据存储和解析功能
"""

import os
import sys
import unittest
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import db
from models.time_series_data import TimeSeriesData
from services.time_series_service import TimeSeriesService
from app import create_app


class TestSimplifiedTimeSeries(unittest.TestCase):
    """简化时序数据模型测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        # 设置为开发环境
        os.environ['FLASK_ENV'] = 'development'
        
        # 创建Flask应用
        cls.app = create_app()
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # 创建数据库表
        db.create_all()
        
        # 创建服务实例
        cls.time_series_service = TimeSeriesService()
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        db.session.remove()
        db.drop_all()
        cls.app_context.pop()
    
    def setUp(self):
        """每个测试前的设置"""
        # 清理测试数据
        db.session.query(TimeSeriesData).delete()
        db.session.commit()
    
    def test_write_sensor_data(self):
        """测试写入传感器数据"""
        device_id = "test_device_001"
        
        # 测试数据
        test_data = {
            'bl0910_error_count': 5,
            'bl0910_rms_values': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900],
            'relay_state': 0b1010101010101010,  # 16位继电器状态
            'short_period_error_count': 2,
            'long_period_error_count': 1,
            'last_zero_cross_time': 123456789,
            'voltage': 220.5,  # 将被转换为22050
            'temperature': 35.8,  # 将被转换为3580
            'total_power': 1500.25,  # 将被转换为150025
            'csq': 25,
            'ber': 3,
            'relay_pull_fault': 0b0001,
            'relay_open_fault': 0b0010
        }
        
        # 写入数据
        success = self.time_series_service.write_sensor_data(device_id, **test_data)
        self.assertTrue(success, "写入传感器数据应该成功")
        
        # 验证数据
        records = db.session.query(TimeSeriesData).filter_by(device_id=device_id).all()
        self.assertEqual(len(records), 1, "应该有一条记录")
        
        record = records[0]
        self.assertEqual(record.device_id, device_id)
        self.assertEqual(record.bl0910_error_count, 5)
        self.assertEqual(len(record.bl0910_rms_values), 10)
        self.assertEqual(record.bl0910_rms_values[0], 1000)
        self.assertEqual(record.bl0910_rms_values[9], 1900)
        self.assertEqual(record.relay_state, 0b1010101010101010)
        self.assertEqual(record.short_period_error_count, 2)
        self.assertEqual(record.long_period_error_count, 1)
        self.assertEqual(record.last_zero_cross_time, 123456789)
        self.assertEqual(record.voltage_raw, 22050)
        self.assertEqual(record.temperature_raw, 3580)
        self.assertEqual(record.total_power_raw, 150025)
        self.assertEqual(record.csq, 25)
        self.assertEqual(record.ber, 3)
        self.assertEqual(record.relay_pull_fault, 0b0001)
        self.assertEqual(record.relay_open_fault, 0b0010)
    
    def test_data_parsing_methods(self):
        """测试数据解析方法"""
        # 创建测试记录
        record = TimeSeriesData(
            device_id="test_device_002",
            timestamp=datetime.now(),
            bl0910_rms_values=[2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900],
            relay_state=0b1111000011110000,
            voltage_raw=22050,  # 220.50V
            temperature_raw=3580,  # 35.80°C
            total_power_raw=150025,  # 1500.25W
            bl0910_error_count=3,
            short_period_error_count=1,
            long_period_error_count=2,
            relay_pull_fault=0b0101,
            relay_open_fault=0b1010
        )
        
        db.session.add(record)
        db.session.commit()
        
        # 测试电压解析
        voltage = record.get_voltage()
        self.assertAlmostEqual(voltage, 220.50, places=2)
        
        # 测试温度解析
        temperature = record.get_temperature()
        self.assertAlmostEqual(temperature, 35.80, places=2)
        
        # 测试总功率解析
        total_power = record.get_total_power()
        self.assertAlmostEqual(total_power, 1500.25, places=2)
        
        # 测试继电器状态解析
        relay_states = record.get_relay_states()
        self.assertTrue(relay_states['relay_1'])  # 第0位为1
        self.assertTrue(relay_states['relay_2'])  # 第1位为1
        self.assertTrue(relay_states['relay_3'])  # 第2位为1
        self.assertTrue(relay_states['relay_4'])  # 第3位为1
        self.assertFalse(relay_states['relay_5'])  # 第4位为0
        
        # 测试通道功率获取
        channel_powers = record.get_all_channel_powers()
        self.assertEqual(len(channel_powers), 10)
        self.assertEqual(channel_powers[0], 2000.0)
        self.assertEqual(channel_powers[9], 2900.0)
        
        # 测试单个通道功率
        power_ch1 = record.get_channel_power(1)
        self.assertEqual(power_ch1, 2000.0)
        
        power_ch10 = record.get_channel_power(10)
        self.assertEqual(power_ch10, 2900.0)
        
        # 测试错误检查
        has_errors = record.has_errors()
        self.assertTrue(has_errors)
    
    def test_hardware_data_parsing(self):
        """测试硬件数据解析"""
        device_id = "test_device_003"
        
        # 模拟硬件信息查询响应数据
        # 这里创建一个简化的测试数据包
        hardware_data = bytearray(60)  # 足够的长度
        
        pos = 0
        # 会话ID (2字节)
        hardware_data[pos:pos+2] = (0x1234).to_bytes(2, byteorder='little')
        pos += 2
        
        # 响应结果 (1字节) - 成功
        hardware_data[pos] = 0
        pos += 1
        
        # BL0910错误计数 (4字节)
        hardware_data[pos:pos+4] = (10).to_bytes(4, byteorder='little')
        pos += 4
        
        # 10个通道的BL0910 RMS寄存器值 (每个4字节)
        for i in range(10):
            value = 3000 + i * 100
            hardware_data[pos:pos+4] = value.to_bytes(4, byteorder='little')
            pos += 4
        
        # 继电器状态 (2字节)
        hardware_data[pos:pos+2] = (0b1010101010101010).to_bytes(2, byteorder='little')
        pos += 2
        
        # 短周期错误计数 (2字节)
        hardware_data[pos:pos+2] = (5).to_bytes(2, byteorder='little')
        pos += 2
        
        # 长周期错误计数 (2字节)
        hardware_data[pos:pos+2] = (3).to_bytes(2, byteorder='little')
        pos += 2
        
        # 最后零交叉时间 (4字节)
        hardware_data[pos:pos+4] = (987654321).to_bytes(4, byteorder='little')
        pos += 4
        
        # 电压 (2字节, 已经 * 100)
        hardware_data[pos:pos+2] = (22050).to_bytes(2, byteorder='little')
        pos += 2
        
        # 温度 (2字节, 已经 * 100)
        hardware_data[pos:pos+2] = (3580).to_bytes(2, byteorder='little')
        pos += 2
        
        # 总有功功率 (4字节, 已经 * 100)
        hardware_data[pos:pos+4] = (150025).to_bytes(4, byteorder='little')
        pos += 4
        
        # 信号质量 (1字节)
        hardware_data[pos] = 28
        pos += 1
        
        # 误码率 (1字节)
        hardware_data[pos] = 2
        pos += 1
        
        # 继电器拉合故障 (2字节)
        hardware_data[pos:pos+2] = (0b0011).to_bytes(2, byteorder='little')
        pos += 2
        
        # 继电器分断故障 (2字节)
        hardware_data[pos:pos+2] = (0b1100).to_bytes(2, byteorder='little')
        pos += 2
        
        # 写入硬件数据
        success = self.time_series_service.write_hardware_info_data(device_id, bytes(hardware_data))
        self.assertTrue(success, "写入硬件数据应该成功")
        
        # 验证解析结果
        records = db.session.query(TimeSeriesData).filter_by(device_id=device_id).all()
        self.assertEqual(len(records), 1, "应该有一条记录")
        
        record = records[0]
        self.assertEqual(record.bl0910_error_count, 10)
        self.assertEqual(record.bl0910_rms_values[0], 3000)
        self.assertEqual(record.bl0910_rms_values[9], 3900)
        self.assertEqual(record.relay_state, 0b1010101010101010)
        self.assertEqual(record.short_period_error_count, 5)
        self.assertEqual(record.long_period_error_count, 3)
        self.assertEqual(record.last_zero_cross_time, 987654321)
        self.assertEqual(record.voltage_raw, 22050)
        self.assertEqual(record.temperature_raw, 3580)
        self.assertEqual(record.total_power_raw, 150025)
        self.assertEqual(record.csq, 28)
        self.assertEqual(record.ber, 2)
        self.assertEqual(record.relay_pull_fault, 0b0011)
        self.assertEqual(record.relay_open_fault, 0b1100)
        
        # 测试解析后的值
        self.assertAlmostEqual(record.get_voltage(), 220.50, places=2)
        self.assertAlmostEqual(record.get_temperature(), 35.80, places=2)
        self.assertAlmostEqual(record.get_total_power(), 1500.25, places=2)
    
    def test_query_methods(self):
        """测试查询方法"""
        device_id = "test_device_004"
        
        # 创建测试数据
        test_records = []
        base_time = datetime.now() - timedelta(hours=2)
        
        for i in range(5):
            record = TimeSeriesData(
                device_id=device_id,
                timestamp=base_time + timedelta(minutes=i*10),
                voltage_raw=22000 + i*50,  # 220.00V, 220.50V, 221.00V, ...
                temperature_raw=3500 + i*20,  # 35.00°C, 35.20°C, 35.40°C, ...
                total_power_raw=150000 + i*1000,  # 1500.00W, 1510.00W, 1520.00W, ...
                csq=20 + i,
                ber=i
            )
            test_records.append(record)
        
        db.session.add_all(test_records)
        db.session.commit()
        
        # 测试查询电压数据
        start_time = base_time - timedelta(minutes=5)
        end_time = base_time + timedelta(hours=1)
        
        voltage_data = self.time_series_service.query_voltage_data(device_id, start_time, end_time)
        self.assertEqual(len(voltage_data), 5)
        self.assertAlmostEqual(voltage_data[0]['value'], 220.00, places=2)
        self.assertAlmostEqual(voltage_data[4]['value'], 222.00, places=2)
        
        # 测试查询温度数据
        temperature_data = self.time_series_service.query_temperature_data(device_id, start_time, end_time)
        self.assertEqual(len(temperature_data), 5)
        self.assertAlmostEqual(temperature_data[0]['value'], 35.00, places=2)
        self.assertAlmostEqual(temperature_data[4]['value'], 35.80, places=2)
        
        # 测试查询总功率数据
        power_data = self.time_series_service.query_total_power_data(device_id, start_time, end_time)
        self.assertEqual(len(power_data), 5)
        self.assertAlmostEqual(power_data[0]['value'], 1500.00, places=2)
        self.assertAlmostEqual(power_data[4]['value'], 1540.00, places=2)
        
        # 测试查询信号质量数据
        csq_data = self.time_series_service.query_csq_data(device_id, start_time, end_time)
        self.assertEqual(len(csq_data), 5)
        self.assertEqual(csq_data[0]['value'], 20)
        self.assertEqual(csq_data[4]['value'], 24)
        self.assertEqual(csq_data[0]['ber'], 0)
        self.assertEqual(csq_data[4]['ber'], 4)


if __name__ == '__main__':
    unittest.main()
