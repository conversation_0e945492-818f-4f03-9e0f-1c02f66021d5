<!-- 无线充电信息查询模态框 -->
<div class="modal fade" id="wtcInfoModal" tabindex="-1" aria-labelledby="wtcInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wtcInfoModalLabel">设备无线充电信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="wtcInfoLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备无线充电信息...</p>
                </div>
                <div id="wtcInfoContent" class="d-none">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">基本信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 60%;">基准插座ID:</th>
                                                <td id="wtc_base_plug_id">--</td>
                                            </tr>
                                            <tr>
                                                <th>插座数量:</th>
                                                <td id="wtc_max_plug_num">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">状态统计</h6>
                                </div>
                                <div class="card-body">
                                    <div id="wtc_status_stats">
                                        <!-- 状态统计将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">无线充电插座详情</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>插座ID</th>
                                                    <th>电压 (V)</th>
                                                    <th>电流 (A)</th>
                                                    <th>功率 (W)</th>
                                                    <th>状态</th>
                                                    <th>状态描述</th>
                                                </tr>
                                            </thead>
                                            <tbody id="wtcInfoTableBody">
                                                <!-- 无线充电信息将通过JavaScript动态填充 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>状态说明</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong>未连接</strong>: 接收模块未连接</li>
                                            <li><strong>空闲</strong>: 接收模块已连接，但未启动充电</li>
                                            <li><strong>充电中</strong>: 接收模块已连接，且已经启动充电</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong>资金不足</strong>: 由于资金不足停止充电</li>
                                            <li><strong>充电完成</strong>: 充电完成停止充电</li>
                                            <li><strong>请求超时</strong>: 由于启动充电请求超时停止充电</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="wtcInfoError" class="alert alert-danger d-none">
                    获取设备无线充电信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryWtcInfo(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<script>
// 查询设备无线充电信息
function queryWtcInfo(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('wtcInfoModal'));
        modal.show();
    }

    // 显示加载中状态
    document.getElementById('wtcInfoLoading').classList.remove('d-none');
    document.getElementById('wtcInfoContent').classList.add('d-none');
    document.getElementById('wtcInfoError').classList.add('d-none');

    // 发送请求获取设备无线充电信息
    fetch(`/api/device/${window.deviceId}/wtc_info`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('wtcInfoLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('wtcInfoError').textContent = '获取设备无线充电信息失败: ' + data.error;
                document.getElementById('wtcInfoError').classList.remove('d-none');
                return;
            }

            // 解析并显示无线充电信息
            displayWtcInfo(data);

            // 显示内容区域
            document.getElementById('wtcInfoContent').classList.remove('d-none');
        })
        .catch(error => {
            console.error('获取设备无线充电信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('wtcInfoLoading').classList.add('d-none');
            document.getElementById('wtcInfoError').textContent = '获取设备无线充电信息失败: ' + error.message;
            document.getElementById('wtcInfoError').classList.remove('d-none');
        });
}

// 显示无线充电信息
function displayWtcInfo(data) {
    // 检查是否有无线充电信息
    if (!data || !data.info) {
        document.getElementById('wtcInfoError').textContent = '设备返回的无线充电信息格式不正确';
        document.getElementById('wtcInfoError').classList.remove('d-none');
        return;
    }

    const info = data.info;

    // 更新基本信息
    document.getElementById('wtc_base_plug_id').textContent = info.base_plug_id || '--';
    document.getElementById('wtc_max_plug_num').textContent = info.max_plug_num || '--';

    // 统计状态
    const statusStats = {};
    let totalPower = 0;
    
    if (info.wireless_plugs) {
        info.wireless_plugs.forEach(plug => {
            const status = plug.status_text || '未知';
            statusStats[status] = (statusStats[status] || 0) + 1;
            totalPower += plug.power || 0;
        });
    }

    // 更新状态统计
    const statsContainer = document.getElementById('wtc_status_stats');
    let statsHtml = '<div class="row">';
    
    Object.entries(statusStats).forEach(([status, count]) => {
        const badgeClass = getStatusBadgeClass(status);
        statsHtml += `
            <div class="col-md-3 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span>${status}:</span>
                    <span class="badge ${badgeClass}">${count}</span>
                </div>
            </div>
        `;
    });
    
    statsHtml += `
        <div class="col-md-3 mb-2">
            <div class="d-flex justify-content-between align-items-center">
                <span>总功率:</span>
                <span class="badge bg-primary">${totalPower.toFixed(3)} W</span>
            </div>
        </div>
    `;
    statsHtml += '</div>';
    
    statsContainer.innerHTML = statsHtml;

    // 填充插座详情表格
    const tableBody = document.getElementById('wtcInfoTableBody');
    tableBody.innerHTML = '';

    if (info.wireless_plugs && info.wireless_plugs.length > 0) {
        info.wireless_plugs.forEach(plug => {
            const row = document.createElement('tr');
            
            // 插座ID
            const idCell = document.createElement('td');
            idCell.innerHTML = `<strong>${plug.plug_id}</strong>`;
            row.appendChild(idCell);
            
            // 电压
            const voltageCell = document.createElement('td');
            voltageCell.textContent = plug.voltage.toFixed(3);
            row.appendChild(voltageCell);
            
            // 电流
            const currentCell = document.createElement('td');
            currentCell.textContent = plug.current.toFixed(3);
            row.appendChild(currentCell);
            
            // 功率
            const powerCell = document.createElement('td');
            powerCell.innerHTML = plug.power ? `<span class="badge bg-success">${plug.power.toFixed(3)} W</span>` : '--';
            row.appendChild(powerCell);
            
            // 状态码
            const statusCodeCell = document.createElement('td');
            statusCodeCell.innerHTML = `<code>0x${plug.txer_status.toString(16).toUpperCase().padStart(2, '0')}</code>`;
            row.appendChild(statusCodeCell);
            
            // 状态描述
            const statusCell = document.createElement('td');
            const badgeClass = getStatusBadgeClass(plug.status_text);
            statusCell.innerHTML = `<span class="badge ${badgeClass}">${plug.status_text}</span>`;
            row.appendChild(statusCell);
            
            tableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = 6;
        cell.className = 'text-center text-muted';
        cell.textContent = '暂无无线充电插座信息';
        row.appendChild(cell);
        tableBody.appendChild(row);
    }
}

// 获取状态对应的徽章样式
function getStatusBadgeClass(status) {
    switch (status) {
        case '充电中':
            return 'bg-success';
        case '空闲':
            return 'bg-info';
        case '未连接':
        case '未连接发射端':
            return 'bg-secondary';
        case '资金不足':
        case '请求超时':
            return 'bg-warning';
        case '充电完成':
            return 'bg-primary';
        default:
            return 'bg-light text-dark';
    }
}
</script>
